2025-06-21 17:51:38,052 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 17:51:38,052 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 17:51:38,053 - signal_handler - INFO - Loaded session string from C:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 17:51:38,053 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750508498
2025-06-21 17:51:38,053 - signal_handler - INFO - Using session path: C:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750508498
2025-06-21 17:51:38,053 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 17:51:38,061 - signal_handler - INFO - Direct signal callback registered
2025-06-21 17:51:38,063 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 17:51:38,063 - signal_handler - INFO - API ID loaded: True
2025-06-21 17:51:38,063 - signal_handler - INFO - API Hash loaded: True
2025-06-21 17:51:38,063 - signal_handler - INFO - Phone number loaded: True
2025-06-21 17:51:38,063 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 17:51:38,064 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 17:51:38,064 - signal_handler - INFO - Using saved session string
2025-06-21 17:51:38,064 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 17:51:38,548 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:51:38,626 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:51:38,626 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 17:51:38,626 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:51:38,706 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:51:38,706 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 17:51:38,706 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:51:38,784 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:51:38,784 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 17:51:38,784 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 17:51:38,785 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 17:51:38,975 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 17:51:38,976 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 17:51:38,976 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 17:51:38,976 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 17:51:38,977 - signal_handler - INFO - Direct signal callback registered
2025-06-21 17:51:39,225 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 17:51:46,290 - signal_handler - DEBUG - Signal processing DISABLED - ignoring message: [REFRESH] Update: **$OSCAR**

[ROCKET] x47.7
[CHAR...
2025-06-21 17:51:53,589 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 17:51:53,589 - signal_handler - INFO - API ID loaded: True
2025-06-21 17:51:53,590 - signal_handler - INFO - API Hash loaded: True
2025-06-21 17:51:53,590 - signal_handler - INFO - Phone number loaded: True
2025-06-21 17:51:53,590 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 17:51:53,590 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 17:51:53,590 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:51:53,679 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:51:53,680 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 17:51:53,680 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:51:53,761 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:51:53,761 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 17:51:53,761 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:51:53,839 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:51:53,839 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 17:51:53,839 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 17:51:53,839 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 17:51:53,948 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 17:51:53,950 - signal_handler - INFO - [msg_1750508513950_4070] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 17:51:53
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 17:51:53,950 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 17:51:53,950 - signal_handler - INFO - [msg_1750508513950_4070] Message queued with normal priority
2025-06-21 17:51:54,066 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 17:51:54,067 - signal_handler - INFO - [msg_1750508513950_4070] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:51:54,362 - signal_handler - INFO - [msg_1750508513950_4070] Successfully resolved info channel entity: Bot Info
2025-06-21 17:51:54,460 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 17:51:54,461 - signal_handler - INFO - Direct signal callback registered
2025-06-21 17:51:54,461 - signal_handler - INFO - [msg_1750508514461_7306] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capital: 1.5...
2025-06-21 17:51:54,461 - signal_handler - INFO - [msg_1750508514461_7306] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:51:54,473 - signal_handler - INFO - [msg_1750508513950_4070] Message sent directly successfully using entity
2025-06-21 17:51:54,474 - signal_handler - INFO - [msg_1750508513950_4070] Message sent successfully via direct send method
2025-06-21 17:51:54,474 - signal_handler - INFO - [msg_1750508513950_4070] Message sent successfully on attempt 1
2025-06-21 17:51:54,540 - signal_handler - INFO - [msg_1750508514461_7306] Successfully resolved info channel entity: Bot Info
2025-06-21 17:51:54,661 - signal_handler - INFO - [msg_1750508514461_7306] Message sent directly successfully using entity
2025-06-21 17:51:54,662 - signal_handler - INFO - [msg_1750508514461_7306] Message sent directly successfully
2025-06-21 17:52:14,661 - signal_handler - INFO - [msg_1750508534661_8640] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 17:52:14

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 17:52:14,662 - signal_handler - INFO - [msg_1750508534661_8640] Message queued with normal priority
2025-06-21 17:52:14,678 - signal_handler - INFO - [msg_1750508534678_7364] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 17:52:14
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 17:52:14,678 - signal_handler - INFO - [msg_1750508534678_7364] Message queued with normal priority
2025-06-21 17:52:14,874 - signal_handler - INFO - [msg_1750508534661_8640] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:52:14,953 - signal_handler - INFO - [msg_1750508534661_8640] Successfully resolved info channel entity: Bot Info
2025-06-21 17:52:15,063 - signal_handler - INFO - [msg_1750508534661_8640] Message sent directly successfully using entity
2025-06-21 17:52:15,064 - signal_handler - INFO - [msg_1750508534661_8640] Message sent successfully via direct send method
2025-06-21 17:52:15,064 - signal_handler - INFO - [msg_1750508534661_8640] Message sent successfully on attempt 1
2025-06-21 17:52:15,064 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 17:52:15,573 - signal_handler - INFO - [msg_1750508534678_7364] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:52:15,653 - signal_handler - INFO - [msg_1750508534678_7364] Successfully resolved info channel entity: Bot Info
2025-06-21 17:52:15,761 - signal_handler - INFO - [msg_1750508534678_7364] Message sent directly successfully using entity
2025-06-21 17:52:15,761 - signal_handler - INFO - [msg_1750508534678_7364] Message sent successfully via direct send method
2025-06-21 17:52:15,761 - signal_handler - INFO - [msg_1750508534678_7364] Message sent successfully on attempt 1
2025-06-21 17:52:29,779 - signal_handler - INFO - [msg_1750508549779_9240] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 17:52:29

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 17:52:29,779 - signal_handler - INFO - [msg_1750508549779_9240] Message queued with normal priority
2025-06-21 17:52:29,793 - signal_handler - INFO - [msg_1750508549793_2645] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 17:52:29
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 17:52:29,793 - signal_handler - INFO - [msg_1750508549793_2645] Message queued with normal priority
2025-06-21 17:52:30,204 - signal_handler - INFO - [msg_1750508549779_9240] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:52:30,285 - signal_handler - INFO - [msg_1750508549779_9240] Successfully resolved info channel entity: Bot Info
2025-06-21 17:52:30,410 - signal_handler - INFO - [msg_1750508549779_9240] Message sent directly successfully using entity
2025-06-21 17:52:30,410 - signal_handler - INFO - [msg_1750508549779_9240] Message sent successfully via direct send method
2025-06-21 17:52:30,410 - signal_handler - INFO - [msg_1750508549779_9240] Message sent successfully on attempt 1
2025-06-21 17:52:30,410 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 17:52:30,917 - signal_handler - INFO - [msg_1750508549793_2645] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:52:30,997 - signal_handler - INFO - [msg_1750508549793_2645] Successfully resolved info channel entity: Bot Info
2025-06-21 17:52:31,110 - signal_handler - INFO - [msg_1750508549793_2645] Message sent directly successfully using entity
2025-06-21 17:52:31,111 - signal_handler - INFO - [msg_1750508549793_2645] Message sent successfully via direct send method
2025-06-21 17:52:31,111 - signal_handler - INFO - [msg_1750508549793_2645] Message sent successfully on attempt 1
