#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main entry point for the Solana Trading Bot
"""

import asyncio
import logging
import os
import sys
import traceback
from datetime import datetime
from typing import Dict, Any

# CRITICAL FIX: Set console encoding to UTF-8 to handle emojis
if sys.platform == "win32":
    try:
        # Try to set console to UTF-8 mode
        os.system("chcp 65001 > nul")
        # Set stdout and stderr encoding
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except Exception:
        # If UTF-8 setup fails, we'll rely on the EmojiSafeFormatter
        pass

# Shared utilities removed - using built-in implementations only
SHARED_UTILS_AVAILABLE = False

from config_manager import ConfigManager
from bot_controller import BotController
from cli_interface import CLIInterface
from state_manager import StateManager


# Simple pump analyzer integration will be initialized after logger setup
SIMPLE_PUMP_AVAILABLE = False
_fast_analysis_function = None

# Configure logging - using built-in implementation only
os.makedirs('logs', exist_ok=True)
os.makedirs('logs/signals', exist_ok=True)  # Create directory for signal logs

# Main log file
log_file = f'logs/bot_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
# Signal-specific log file
signal_log_file = f'logs/signals/signal_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'

# OPTIMIZED: Centralized emoji handling - single source of truth
class EmojiHandler:
    """Centralized emoji handling for consistent processing across the application"""

    # Single emoji mapping - eliminates duplication
    EMOJI_REPLACEMENTS = {
        '🔥': '[FIRE]', '✅': '[SUCCESS]', '❌': '[ERROR]', '⚠️': '[WARNING]',
        '💰': '[MONEY]', '🚨': '[ALERT]', '🛡️': '[SHIELD]', '🟣': '[PURPLE]',
        '🔴': '[RED]', '🚀': '[ROCKET]', '📊': '[CHART]', '💎': '[DIAMOND]',
        '🎯': '[TARGET]', '⏰': '[CLOCK]', '🔍': '[SEARCH]', '📈': '[TRENDING_UP]',
        '📉': '[TRENDING_DOWN]', '💸': '[MONEY_WINGS]', '🎉': '[PARTY]', '⭐': '[STAR]',
        '🌟': '[GLOWING_STAR]', '🔔': '[BELL]', '📱': '[PHONE]', '💻': '[COMPUTER]',
        '🎮': '[GAME]', '🏆': '[TROPHY]', '🎊': '[CONFETTI]', '🙈': '[SEE_NO_EVIL]',
        '🙉': '[HEAR_NO_EVIL]', '🙊': '[SPEAK_NO_EVIL]', '🪙': '[COIN]', '➡️': '[RIGHT_ARROW]',
        '⬆️': '[UP_ARROW]', '⬇️': '[DOWN_ARROW]', '⬅️': '[LEFT_ARROW]', '🔄': '[REFRESH]',
        '🔃': '[CLOCKWISE]', '🔂': '[REPEAT]', '🔁': '[REPEAT_ONE]', '💯': '[HUNDRED]',
        '🎪': '[CIRCUS]', '🎭': '[MASKS]', '🎨': '[PALETTE]', '🎬': '[CLAPPER]',
        '🎤': '[MICROPHONE]', '🎧': '[HEADPHONES]', '🎵': '[MUSICAL_NOTE]', '🎶': '[MUSICAL_NOTES]'
    }

    @classmethod
    def clean_message(cls, message):
        """Clean a message of emojis and problematic Unicode characters"""
        if not isinstance(message, str):
            message = str(message)

        # Replace emojis with text equivalents
        for emoji, replacement in cls.EMOJI_REPLACEMENTS.items():
            message = message.replace(emoji, replacement)

        # ENHANCED: Remove any remaining problematic Unicode characters
        try:
            message.encode('cp1252')
        except UnicodeEncodeError:
            # Optimized: Use translate instead of character-by-character processing
            import string
            printable = set(string.printable)
            message = ''.join(char if char in printable else '[?]' for char in message)

        return message

# Custom formatter to handle emojis in Windows console - ENHANCED VERSION
class EmojiSafeFormatter(logging.Formatter):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Use centralized emoji handler
        self.emoji_handler = EmojiHandler

    def format(self, record):
        # CRITICAL FIX: Clean the record message BEFORE formatting using centralized handler
        if hasattr(record, 'msg') and record.msg:
            record.msg = self.emoji_handler.clean_message(record.msg)

        # Also clean any args that might contain emojis
        if hasattr(record, 'args') and record.args:
            cleaned_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    cleaned_args.append(self.emoji_handler.clean_message(arg))
                else:
                    cleaned_args.append(arg)
            record.args = tuple(cleaned_args)

        # Now format the cleaned record
        formatted_message = super().format(record)

        # Final safety check
        try:
            formatted_message.encode('cp1252')
        except UnicodeEncodeError:
            formatted_message = ''.join(c for c in formatted_message if ord(c) < 256)

        return formatted_message

def safe_log_message(message):
    """
    OPTIMIZED: Convert a message with emojis to a safe format for logging.
    Uses centralized EmojiHandler to eliminate code duplication.
    """
    return EmojiHandler.clean_message(message)

# Configure logging
log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Main file handler
file_handler = logging.FileHandler(log_file)
file_handler.setFormatter(logging.Formatter(log_format))

# Signal-specific file handler
signal_file_handler = logging.FileHandler(signal_log_file)
signal_file_handler.setFormatter(logging.Formatter(log_format))

# Console handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(EmojiSafeFormatter(log_format))

# Configure root logger
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# CRITICAL FIX: Apply EmojiSafeFormatter to ALL loggers to prevent Unicode errors
emoji_safe_console_handler = logging.StreamHandler()
emoji_safe_console_handler.setFormatter(EmojiSafeFormatter(log_format))

# Set specific loggers to DEBUG level for detailed logging in log files
for logger_name in ['api_clients', 'bot_controller', 'token_analyzer', 'analysis_worker_pool', 'signal_handler', '__main__', 'state_manager', 'config_manager', 'trade_executor', 'websocket_manager', 'execution_queue']:
    specific_logger = logging.getLogger(logger_name)
    specific_logger.setLevel(logging.DEBUG)
    # Remove any existing console handlers to avoid duplicates
    for handler in specific_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler):
            specific_logger.removeHandler(handler)
    # Add our emoji-safe console handler
    specific_logger.addHandler(emoji_safe_console_handler)
    # Prevent propagation to avoid double logging
    specific_logger.propagate = False

# Configure signal_handler logger with its own file handler (already configured above)
signal_logger = logging.getLogger('signal_handler')
# Add the signal file handler for dedicated signal logging
signal_logger.addHandler(signal_file_handler)

# Reduce verbosity for Telethon
logging.getLogger('telethon').setLevel(logging.WARNING)
logging.getLogger('telethon.network.mtprotosender').setLevel(logging.ERROR)
logging.getLogger('asyncio').setLevel(logging.WARNING)

# Create a logger for this module
logger = logging.getLogger(__name__)

# Initialize simple_pump_analyzer integration after logger setup
def init_simple_pump_analyzer():
    """Initialize simple_pump_analyzer functions for fast token analysis"""
    global SIMPLE_PUMP_AVAILABLE, _fast_analysis_function

    try:
        # OPTIMIZED: Import required modules for integrated analysis functions
        import requests
        import time  # Needed for time.time() and time.sleep() calls
        from concurrent.futures import ThreadPoolExecutor
        from helius_rate_limiter import get_helius_rate_limiter

        # Get RPC URL from environment
        helius_api_key = os.getenv('HELIUS_API_KEY')
        if not helius_api_key:
            raise ValueError("HELIUS_API_KEY environment variable not set")
        RPC_URL = f"https://mainnet.helius-rpc.com/?api-key={helius_api_key}"

        # Initialize Helius rate limiter
        global _helius_limiter
        _helius_limiter = get_helius_rate_limiter()

        # Global caches for performance (module level)
        global _sol_price_cache, _token_supply_cache, _analysis_session, _token_analysis_cache, _cache_lock
        _sol_price_cache = {"price": None, "timestamp": 0}
        _token_supply_cache = {}
        _analysis_session = None
        _token_analysis_cache = {}  # token_address -> {"timestamp": time, "result": "rejected", "reason": "low_liquidity"}
        # SURGICAL FIX: Add thread safety for cache operations
        _cache_lock = asyncio.Lock()

        # SURGICAL FIX: PERMANENT SEPARATION - Analysis vs Position Monitoring
        global _active_analyses, _analysis_lock, _api_call_timestamps, _monitoring_cache, _signal_cache
        global PERMANENTLY_SKIPPED_TOKENS, PERMANENTLY_ANALYZED_TOKENS
        global TOKEN_TIMESTAMPS, LAST_CLEANUP_TIME

        _active_analyses = set()
        _analysis_lock = asyncio.Lock()

        # BULLETPROOF: Token-level concurrency protection to prevent liquidity conflicts
        _token_data_locks = {}  # Token-specific locks for data access
        _token_data_cache = {}  # Shared cache to prevent duplicate API calls

        async def get_token_data_lock(token_address: str):
            """Get or create a token-specific lock for data access"""
            if token_address not in _token_data_locks:
                _token_data_locks[token_address] = asyncio.Lock()
            return _token_data_locks[token_address]

        # BULLETPROOF: Permanent token state tracking with memory management
        PERMANENTLY_SKIPPED_TOKENS = set()      # Tokens permanently skipped - NEVER analyze again
        PERMANENTLY_ANALYZED_TOKENS = set()     # Tokens already processed (bought OR skipped)
        # REMOVED: BOUGHT_POSITIONS_ONLY - now using StateManager.is_position_open() instead

        # MEMORY MANAGEMENT: Track token timestamps for cleanup
        TOKEN_TIMESTAMPS = {}                   # token_address -> timestamp when added
        LAST_CLEANUP_TIME = time.time()         # Last cleanup execution time

        # SURGICAL FIX: API-specific rate limiting and cache separation
        _api_call_timestamps = {}  # For rate limiting per API per token
        _monitoring_cache = {}     # For position monitoring ONLY
        _signal_cache = {}         # For signal processing ONLY

        # SURGICAL FIX: API-specific rate limits (moved to helius_rate_limiter.py)
        # Removed unused API_RATE_LIMITS - using centralized rate limiting



        def get_analysis_session():
            """Get or create HTTP session for analysis"""
            global _analysis_session
            if _analysis_session is None:
                _analysis_session = requests.Session()
                _analysis_session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
            return _analysis_session

        def get_sol_price():
            """Get SOL price with caching - UNIVERSAL 1-MINUTE CACHE"""
            global _sol_price_cache
            current_time = time.time()

            # Use 1-minute cache (60 seconds) - hardcoded for reliability
            cache_seconds = 60

            # Use cached price if within cache duration
            if (_sol_price_cache["price"] is not None and
                current_time - _sol_price_cache["timestamp"] < cache_seconds):
                logger.debug(f"Using cached SOL price: ${_sol_price_cache['price']} (age: {current_time - _sol_price_cache['timestamp']:.1f}s)")
                return _sol_price_cache["price"]

            # Try with retry logic for reliability
            for attempt in range(2):
                try:
                    session = get_analysis_session()
                    response = session.get(
                        "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd",
                        timeout=8  # Increased timeout for reliability
                    )
                    if response.status_code == 200:
                        data = response.json()
                        price = float(data["solana"]["usd"])
                        _sol_price_cache["price"] = price
                        _sol_price_cache["timestamp"] = current_time
                        logger.info(f"Fetched fresh SOL price: ${price} (attempt {attempt + 1})")
                        return price
                    else:
                        logger.warning(f"CoinGecko API returned status {response.status_code} (attempt {attempt + 1})")
                except Exception as e:
                    logger.error(f"Failed to fetch SOL price (attempt {attempt + 1}): {e}")
                    if attempt == 0:
                        time.sleep(0.5)  # Brief pause before retry

            # Return cached price if available, otherwise use realistic default
            fallback_price = _sol_price_cache["price"] if _sol_price_cache["price"] else 200.0
            logger.warning(f"Using fallback SOL price: ${fallback_price}")
            return fallback_price

        def should_analyze_token_permanent(token_address):
            """BULLETPROOF: Check if token should be analyzed - PERMANENT decision tracking"""
            global PERMANENTLY_ANALYZED_TOKENS, PERMANENTLY_SKIPPED_TOKENS

            # CRITICAL: Once processed, NEVER analyze again
            if token_address in PERMANENTLY_ANALYZED_TOKENS:
                if token_address in PERMANENTLY_SKIPPED_TOKENS:
                    return False, "PERMANENTLY SKIPPED - will never be analyzed again"
                else:
                    return False, "PERMANENTLY PROCESSED - already bought/analyzed"

            return True, "New token - ready for analysis"

        def mark_token_permanent_decision(token_address, decision, reason=None):
            """BULLETPROOF: Mark token as permanently processed with timestamp tracking"""
            global PERMANENTLY_ANALYZED_TOKENS, PERMANENTLY_SKIPPED_TOKENS, TOKEN_TIMESTAMPS

            current_time = time.time()
            PERMANENTLY_ANALYZED_TOKENS.add(token_address)
            TOKEN_TIMESTAMPS[token_address] = current_time

            if decision == "skipped":
                PERMANENTLY_SKIPPED_TOKENS.add(token_address)
                logger.info(f"🚫 PERMANENT SKIP: {token_address} - {reason}")
            elif decision == "bought":
                # Position tracking is now handled by StateManager
                logger.info(f"✅ BOUGHT: {token_address} - position tracked by StateManager")
            elif decision == "sold":
                # Position removal is now handled by StateManager.close_position()
                logger.info(f"💰 SOLD: {token_address} - position closed in StateManager")

            logger.debug(f"PERMANENT STATE: {len(PERMANENTLY_ANALYZED_TOKENS)} total, {len(PERMANENTLY_SKIPPED_TOKENS)} skipped")

            # MEMORY MANAGEMENT: Trigger cleanup if needed
            cleanup_permanent_tokens_if_needed()

        def cleanup_permanent_tokens_if_needed():
            """MEMORY MANAGEMENT: Cleanup old permanent tokens to prevent memory bloat"""
            global PERMANENTLY_ANALYZED_TOKENS, PERMANENTLY_SKIPPED_TOKENS
            global TOKEN_TIMESTAMPS, LAST_CLEANUP_TIME

            current_time = time.time()

            # Run cleanup every 2 hours or when token count exceeds limits (reduced from 6 hours)
            cleanup_interval = 2 * 3600  # 2 hours
            max_tokens = 5000  # Maximum tokens to keep in memory (reduced from 10000)
            max_age_hours = 12  # Remove tokens older than 12 hours (reduced from 24)

            should_cleanup = (
                (current_time - LAST_CLEANUP_TIME) > cleanup_interval or
                len(PERMANENTLY_ANALYZED_TOKENS) > max_tokens
            )

            if not should_cleanup:
                return

            try:
                max_age_seconds = max_age_hours * 3600
                tokens_to_remove = []

                # Find old tokens to remove (but keep bought positions)
                # Note: We can't access StateManager here, so we'll be more conservative with cleanup
                for token_address, timestamp in TOKEN_TIMESTAMPS.items():
                    age = current_time - timestamp
                    if age > max_age_seconds:
                        tokens_to_remove.append(token_address)

                # Remove old tokens
                removed_count = 0
                for token_address in tokens_to_remove:
                    PERMANENTLY_ANALYZED_TOKENS.discard(token_address)
                    PERMANENTLY_SKIPPED_TOKENS.discard(token_address)
                    TOKEN_TIMESTAMPS.pop(token_address, None)
                    removed_count += 1

                LAST_CLEANUP_TIME = current_time

                if removed_count > 0:
                    logger.info(f"🧹 MEMORY CLEANUP: Removed {removed_count} old tokens. "
                              f"Remaining: {len(PERMANENTLY_ANALYZED_TOKENS)} analyzed, "
                              f"{len(PERMANENTLY_SKIPPED_TOKENS)} skipped")
                else:
                    logger.debug("🧹 MEMORY CLEANUP: No old tokens to remove")

            except Exception as e:
                logger.error(f"Error during token cleanup: {e}")

        def remove_sold_position(token_address, reason=None):
            """BULLETPROOF: Log position closure (StateManager handles the actual removal)"""
            # Position removal is now handled by StateManager.close_position()
            logger.info(f"💰 POSITION CLOSED: {token_address} - {reason}")

        # REMOVED: validate_production_config_enhanced - unused function
        # Enhanced validation is now handled in the main startup sequence



        def cache_token_analysis(token_address, result, reason=None):
            """Cache token analysis result - SURGICAL TRACKING"""
            global _token_analysis_cache
            _token_analysis_cache[token_address] = {
                "timestamp": time.time(),
                "result": result,
                "reason": reason
            }

        def get_dexscreener_data(token_address, bypass_rate_limit=False):
            """Get DexScreener data - RESTORED ORIGINAL IMPLEMENTATION"""
            # SELL NOTIFICATION FIX: Log when bypassing rate limits
            if bypass_rate_limit:
                logger.info(f"🚀 BYPASSING RATE LIMIT for DexScreener API call: {token_address}")

            # CRITICAL FIX: Use direct HTTP call to avoid event loop conflicts
            try:
                import requests

                # Use direct API call to avoid async conflicts
                url = f"https://api.dexscreener.com/token-pairs/v1/solana/{token_address}"

                headers = {
                    "Accept": "application/json",
                    "User-Agent": "TradingBot/1.0"
                }

                response = requests.get(url, headers=headers, timeout=5)

                if response.status_code == 200:
                    data = response.json()

                    # Handle both response formats
                    pairs = []
                    if isinstance(data, list):
                        pairs = data
                    elif isinstance(data, dict) and 'pairs' in data:
                        pairs = data['pairs']

                    if pairs:
                        # Find best pair (highest liquidity)
                        best_pair = max(pairs, key=lambda p: float(p.get('liquidity', {}).get('usd', 0) or 0))

                        # Extract data
                        base_token = best_pair.get('baseToken', {})
                        result = {
                            'exists': True,
                            'name': base_token.get('name', 'Unknown'),
                            'symbol': base_token.get('symbol', 'Unknown'),
                            'price': float(best_pair.get('priceUsd', 0) or 0),
                            'liquidity': float(best_pair.get('liquidity', {}).get('usd', 0) or 0),
                            'volume24h': float(best_pair.get('volume', {}).get('h24', 0) or 0),
                            'volume5m': float(best_pair.get('volume', {}).get('m5', 0) or 0),
                            'marketCap': float(best_pair.get('marketCap', 0) or 0),
                            'fdv': float(best_pair.get('fdv', 0) or 0),
                            'url': best_pair.get('url', ''),
                            'dex_id': best_pair.get('dexId', ''),
                            'source': 'DexScreener'
                        }

                        logger.info(f"DexScreener call successful for {token_address}")
                        return result
                    else:
                        return {"exists": False, "error": "No pairs found"}
                else:
                    return {"exists": False, "error": f"API error: {response.status_code}"}

            except Exception as e:
                logger.error(f"DexScreener call failed for {token_address}: {e}")
                return {"exists": False, "error": f"Request failed: {str(e)}"}

        def get_token_supply(token_address):
            """Get token supply with caching - ENHANCED with Helius rate limiting"""
            global _token_supply_cache, _helius_limiter

            if token_address in _token_supply_cache:
                return _token_supply_cache[token_address]

            # Use Helius rate limiter for smart retry logic
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTokenSupply",
                "params": [token_address]
            }

            response_data = _helius_limiter.make_rpc_request(RPC_URL, payload, timeout=6)

            if response_data and 'result' in response_data and 'value' in response_data['result']:
                supply_info = response_data['result']['value']
                decimals = int(supply_info.get('decimals', 6))
                amount = int(supply_info.get('amount', 0))
                supply = amount / (10 ** decimals)

                # Cache the result permanently
                _token_supply_cache[token_address] = supply
                logger.debug(f"Token supply success: {supply:,.0f}")
                return supply

            return 1000000000  # Default supply

        def fetch_parallel_data(token_address, bypass_rate_limit=False):
            """Fetch DexScreener data, token supply, and SOL price in parallel"""
            with ThreadPoolExecutor(max_workers=3) as executor:
                # Submit all tasks - pass bypass_rate_limit to DexScreener call
                dex_future = executor.submit(get_dexscreener_data, token_address, bypass_rate_limit)
                supply_future = executor.submit(get_token_supply, token_address)
                sol_price_future = executor.submit(get_sol_price)

                # Get results
                dex_data = dex_future.result()
                supply = supply_future.result()
                sol_price = sol_price_future.result()

                return dex_data, supply, sol_price

        def get_total_holders(token_address, rpc_url):
            """Get total number of token holders - ENHANCED with Helius rate limiting"""
            global _helius_limiter

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getTokenLargestAccounts",
                "params": [token_address]
            }

            response_data = _helius_limiter.make_rpc_request(rpc_url, payload, timeout=6)

            if response_data and 'result' in response_data and 'value' in response_data['result']:
                accounts = response_data['result']['value']
                # Filter out accounts with 0 balance
                non_zero_accounts = [acc for acc in accounts if float(acc.get('amount', 0)) > 0]
                logger.debug(f"Holders success: {len(non_zero_accounts)} holders")
                return len(non_zero_accounts)

            return 0

        def get_pool_balance(pool_address):
            """Get SOL balance of a pool address - ENHANCED with Helius rate limiting"""
            global _helius_limiter

            # DEBUG: Log the exact address being checked
            logger.info(f"🔍 CHECKING SOL BALANCE FOR: {pool_address}")

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [pool_address]
            }

            response_data = _helius_limiter.make_rpc_request(RPC_URL, payload, timeout=6)

            if response_data and 'result' in response_data and 'value' in response_data['result']:
                lamports = response_data['result']['value']
                sol_balance = lamports / 1_000_000_000  # Convert lamports to SOL
                logger.info(f"💰 ADDRESS {pool_address[:8]}... HAS {sol_balance:.6f} SOL ({lamports} lamports)")
                return sol_balance
            else:
                logger.error(f"❌ FAILED TO GET BALANCE FOR {pool_address[:8]}... - Response: {response_data}")

            return 0.0

        def find_pump_fun_pool(token_address):
            """Find pump.fun bonding curve pool - BACK TO WORKING HELIUS METHOD"""
            global _helius_limiter

            try:
                logger.info(f"🔍 Looking for pump.fun bonding curve for {token_address}")

                # Method 1: DIRECT BONDING CURVE DERIVATION - Skip the problematic getProgramAccounts
                # getProgramAccounts is hanging/timing out, so use PDA derivation instead

                # Method 2: DIRECT BONDING CURVE DERIVATION - More reliable than getProgramAccounts
                logger.info(f"🎯 Attempting direct bonding curve derivation for {token_address}")
                try:
                    # Try to derive the bonding curve address using pump.fun's standard derivation
                    # This is more reliable than getProgramAccounts which can timeout
                    import base58
                    from solders.pubkey import Pubkey

                    # Pump.fun program ID
                    PUMP_PROGRAM = Pubkey.from_string("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")
                    token_mint = Pubkey.from_string(token_address)

                    # Derive bonding curve PDA
                    bonding_curve_seed = b"bonding-curve"
                    bonding_curve_pda, _ = Pubkey.find_program_address(
                        [bonding_curve_seed, bytes(token_mint)],
                        PUMP_PROGRAM
                    )

                    bonding_curve_address = str(bonding_curve_pda)
                    logger.info(f"🎯 Derived bonding curve PDA: {bonding_curve_address}")

                    # Check if this derived address has SOL
                    sol_balance = get_pool_balance(bonding_curve_address)
                    logger.info(f"💰 Derived bonding curve SOL: {sol_balance:.6f} SOL")

                    logger.info(f"🔍 PDA DERIVATION CHECK: sol_balance={sol_balance:.6f}, threshold=0.01, passes={sol_balance > 0.01}")
                    if sol_balance > 0.01:  # Even small amounts indicate a real bonding curve
                        logger.info(f"✅ FOUND PUMP.FUN BONDING CURVE VIA PDA DERIVATION: {bonding_curve_address} with {sol_balance:.6f} SOL")
                        logger.info(f"🎯 RETURNING BONDING CURVE ADDRESS: {bonding_curve_address}")
                        return bonding_curve_address
                    else:
                        logger.info(f"❌ PDA derivation found curve but SOL balance too low: {sol_balance:.6f} SOL")

                except Exception as e:
                    logger.error(f"❌ PDA derivation failed with exception: {e}")
                    import traceback
                    logger.error(f"❌ PDA derivation traceback: {traceback.format_exc()}")

                # Method 3: Fallback - check token accounts but look for the one with highest token balance
                logger.info(f"🔄 Fallback: Checking token accounts for {token_address}")
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getTokenLargestAccounts",
                    "params": [token_address]
                }

                response_data = _helius_limiter.make_rpc_request(RPC_URL, payload, timeout=6)

                if response_data and 'result' in response_data and 'value' in response_data['result']:
                    accounts = response_data['result']['value']
                    logger.info(f"🔍 Found {len(accounts)} token accounts")

                    # For pump.fun, the bonding curve usually holds the most tokens
                    # Check the account with the highest token balance
                    if accounts:
                        largest_account = accounts[0]  # First account has the most tokens
                        account_address = largest_account.get('address')

                        if account_address:
                            balance_raw = int(largest_account.get('amount', 0))
                            decimals = int(largest_account.get('decimals', 6))
                            balance_adjusted = balance_raw / (10 ** decimals)

                            logger.info(f"🎯 Largest token holder: {account_address[:8]}... with {balance_adjusted:,.0f} tokens")

                            # Check if this account has significant SOL (indicating it's the bonding curve)
                            sol_balance = get_pool_balance(account_address)
                            logger.info(f"💰 Largest holder SOL balance: {sol_balance:.6f} SOL")

                            # If it has substantial SOL, it's likely the bonding curve
                            if sol_balance > 1.0:
                                logger.info(f"✅ BONDING CURVE FOUND (largest holder): {account_address} with {sol_balance:.6f} SOL")
                                return account_address
                            else:
                                logger.warning(f"⚠️ Largest holder has low SOL balance: {sol_balance:.6f} SOL")

                                # Try a few more accounts
                                for i, account in enumerate(accounts[1:6]):  # Check next 5 accounts
                                    account_address = account.get('address')
                                    if account_address:
                                        sol_balance = get_pool_balance(account_address)
                                        logger.info(f"💰 Account {i+2} SOL balance: {sol_balance:.6f} SOL")

                                        if sol_balance > 1.0:
                                            logger.info(f"✅ BONDING CURVE FOUND (account {i+2}): {account_address} with {sol_balance:.6f} SOL")
                                            return account_address

                logger.warning(f"❌ No pump.fun bonding curve found for {token_address}")

            except Exception as e:
                logger.error(f"Error in find_pump_fun_pool: {e}")

            return None

        async def analyze_new_signal_only(token_address: str, **kwargs) -> Dict[str, Any]:
            """
            ANALYSIS PROCESS: Only for NEW signals - NEVER for position monitoring
            Bulletproof separation with permanent skip tracking
            """
            # BULLETPROOF: Analysis state tracking with token-level concurrency protection
            global _active_analyses, _analysis_lock

            # BULLETPROOF: Get token-specific lock to prevent concurrent data access
            token_lock = await get_token_data_lock(token_address)

            async with _analysis_lock:
                if token_address in _active_analyses:
                    logger.debug(f"Analysis already in progress for {token_address}")
                    return {"status": "in_progress", "skip": True}

                _active_analyses.add(token_address)

            try:
                # SURGICAL FIX: Separate cache tracking per analysis type
                try:
                    force_fresh = kwargs.get('force_fresh', False)
                    skip_notifications = kwargs.get('skip_notifications', False)
                    bypass_rate_limit = kwargs.get('bypass_rate_limit', False)  # NEW: For sell notifications
                    bypass_permanent_check = kwargs.get('bypass_permanent_check', False)  # NEW: For sell notifications

                    # POSITION MONITORING: Use monitoring cache with 200ms rate limit
                    if force_fresh and skip_notifications:
                        cache_key = f"monitor_{token_address}"
                        if cache_key in _monitoring_cache:
                            cache_time = _monitoring_cache[cache_key]
                            if time.time() - cache_time < 0.2:  # 200ms for monitoring
                                logger.debug(f"MONITOR CACHE: Skipping {token_address} (too recent)")
                                return {"cached": True, "reason": "monitoring_rate_limit"}
                        _monitoring_cache[cache_key] = time.time()
                        logger.debug(f"POSITION MONITORING: Fresh analysis for {token_address}")

                    # SIGNAL PROCESSING: Use PERMANENT analysis check (unless bypassed for sell notifications)
                    elif not force_fresh and not bypass_permanent_check:
                        should_analyze, reason = should_analyze_token_permanent(token_address)
                        if not should_analyze:
                            logger.info(f"PERMANENT SKIP: {token_address}: {reason}")
                            return {"cached": True, "reason": reason, "skip": True}

                        # Cache signal analysis
                        signal_key = f"signal_{token_address}"
                        _signal_cache[signal_key] = time.time()

                except Exception as e:
                    logger.warning(f"Cache check failed for {token_address}: {e}")
                    # Continue with analysis if cache check fails

                logger.info(f"[FAST] Analyzing token: {token_address}")
                start_time = time.time()

                # BULLETPROOF: Use parallel data fetching with token-level locking
                async with token_lock:
                    dex_data, supply, sol_price = fetch_parallel_data(token_address, bypass_rate_limit)

                # Log the fetched data for debugging
                logger.info(f"Fetched data - SOL price: ${sol_price}, Supply: {supply:,.0f}")

                # DexScreener data is optional for pump.fun tokens - continue with pump.fun calculation
                if not dex_data:
                    logger.info(f"No DexScreener data for {token_address}, will use pump.fun calculation")
                    dex_data = {}  # Empty dict to avoid errors
                else:
                    logger.info(f"DexScreener data available - Price: ${dex_data.get('price', 0)}, Volume 24h: ${dex_data.get('volume24h', 0):,.0f}")

                # Extract key metrics - CRITICAL FIX: Use correct field names that match what get_dexscreener_data returns
                market_cap = dex_data.get('marketCap', 0)  # FIXED: Use 'marketCap' not 'market_cap'
                price_usd = dex_data.get('price', 0)  # Fixed: use 'price' not 'price_usd'

                # CRITICAL FIX: DexScreener returns liquidity as nested object {usd: value}, not direct number
                dex_liquidity_raw = dex_data.get('liquidity', 0)
                if isinstance(dex_liquidity_raw, dict):
                    dex_liquidity = dex_liquidity_raw.get('usd', 0)  # Extract from nested structure
                else:
                    dex_liquidity = dex_liquidity_raw  # Direct number (from our processed data)

                # ENHANCED DEBUG: Show what DexScreener actually returned
                # BULLETPROOF: Centralized liquidity data normalization
                def normalize_liquidity_data(dex_data, dex_liquidity_raw, dex_liquidity):
                    """Centralized function to ensure consistent liquidity data structure"""
                    normalized_data = {
                        'liquidity': dex_liquidity,
                        'liquidity_usd': dex_liquidity,
                        'liquidity_value': dex_liquidity,
                        'usd_liquidity': dex_liquidity,
                        'raw_liquidity': dex_liquidity_raw,
                        'source': 'DexScreener' if dex_liquidity > 0 else 'None'
                    }

                    if dex_liquidity > 0:
                        logger.info(f"[SCAN] DexScreener liquidity extraction: raw={dex_liquidity_raw}, processed=${dex_liquidity:,.0f}")
                        logger.info(f"[SCAN] Normalized liquidity data: {normalized_data}")
                    else:
                        logger.warning(f"[SCAN] DexScreener liquidity extraction failed: raw={dex_liquidity_raw}, processed=${dex_liquidity:,.0f}")
                        logger.warning(f"[SCAN] DexScreener data available but liquidity is 0 - checking raw data")
                        if isinstance(dex_data, dict) and 'raw_pair_data' in dex_data:
                            raw_pair = dex_data['raw_pair_data']
                            logger.warning(f"[SCAN] Raw pair liquidity: {raw_pair.get('liquidity', 'NOT_FOUND')}")
                        logger.warning(f"[SCAN] This may indicate DexScreener API issues or data structure changes")

                    return normalized_data

                # Apply normalization
                liquidity_data = normalize_liquidity_data(dex_data, dex_liquidity_raw, dex_liquidity)

                if market_cap > 80000 or (dex_liquidity > 0 and dex_liquidity > 100):  # Use DexScreener if MC > 80k OR if it has reasonable liquidity data
                    liquidity_source = "DexScreener"
                    liquidity_value = dex_liquidity
                    show_multiplier = False  # Hide multiplier when using DexScreener data
                    pool_sol = 0  # Not needed for DexScreener liquidity
                    logger.info(f"Using DexScreener liquidity: ${liquidity_value:,.0f} (MC: ${market_cap:,.0f})")
                else:
                    liquidity_source = "Calculated"
                    # Find pump.fun pool and get SOL balance for accurate calculation
                    try:
                        logger.info(f"🔍 Looking for pump.fun pool for {token_address}")
                        pool_address = find_pump_fun_pool(token_address)
                        if pool_address:
                            logger.info(f"✅ Found pool address: {pool_address}")
                            pool_sol = get_pool_balance(pool_address)
                            logger.info(f"💰 Pool SOL balance: {pool_sol}")
                            if pool_sol > 0:
                                liquidity_value = pool_sol * sol_price * 2.0
                                logger.info(f"📊 Calculated liquidity: ${liquidity_value:,.0f} (SOL: {pool_sol} * ${sol_price} * 2.0)")

                                # SURGICAL FIX: If calculated liquidity is extremely low but market cap is high, use DexScreener instead
                                if liquidity_value < 100 and market_cap > 10000 and dex_liquidity > liquidity_value:
                                    logger.warning(f"Calculated liquidity (${liquidity_value:,.0f}) is extremely low compared to market cap (${market_cap:,.0f}). Using DexScreener liquidity instead.")
                                    liquidity_source = "DexScreener (fallback)"
                                    liquidity_value = dex_liquidity
                                    show_multiplier = False
                                    pool_sol = 0

                                # Calculate market cap and price from pump.fun data if DexScreener missing
                                if market_cap == 0:
                                    market_cap = liquidity_value  # For pump.fun, liquidity = market cap
                                    price_usd = market_cap / supply if supply > 0 else 0
                                logger.info(f"Found pool {pool_address[:8]}... with {pool_sol:.6f} SOL")
                            else:
                                pool_sol = 0
                                liquidity_value = 0
                                logger.warning(f"Pool found but no SOL balance: {pool_address}")
                        else:
                            pool_sol = 0
                            # BULLETPROOF: Enhanced fallback with validation
                            if dex_liquidity > 0:
                                liquidity_value = dex_liquidity
                                liquidity_source = "DexScreener (pump.fun pool not found)"
                                logger.warning(f"Could not find pump.fun pool for {token_address}, using DexScreener liquidity: ${liquidity_value:,.0f}")
                            else:
                                liquidity_value = 0
                                liquidity_source = "No liquidity data available"
                                logger.error(f"No liquidity data available for {token_address} - both pump.fun and DexScreener failed")

                            # Only fail if we have no data at all
                            if market_cap == 0 and liquidity_value == 0:
                                return {"exists": False, "reason": "No pump.fun pool found and no DexScreener data"}
                    except Exception as e:
                        logger.error(f"Error finding pump.fun pool: {e}")
                        pool_sol = 0
                        # BULLETPROOF: Enhanced fallback with validation
                        if dex_liquidity > 0:
                            liquidity_value = dex_liquidity
                            liquidity_source = "DexScreener (pump.fun pool lookup failed)"
                            logger.warning(f"Pump.fun pool lookup failed for {token_address}, using DexScreener liquidity: ${liquidity_value:,.0f}")
                        else:
                            liquidity_value = 0
                            liquidity_source = "No liquidity data available"
                            logger.error(f"No liquidity data available for {token_address} - both pump.fun and DexScreener failed")

                        # Only fail if we have no data at all
                        if market_cap == 0 and liquidity_value == 0:
                            return {"exists": False, "reason": f"Pool lookup error: {str(e)}"}

                    show_multiplier = True  # Show x2.0 when using calculated liquidity
                    logger.info(f"Using calculated liquidity: ${liquidity_value:,.0f} (x2.0)")

                # Get holders count using fast Helius RPC
                holders = get_total_holders(token_address, RPC_URL)

                # Get detailed holder analysis (top 10 percentage) - EXACT simple_pump_analyzer logic
                top_10_percentage = 0
                try:
                    # Get top 10 holders data for percentage calculation using rate limiter
                    payload = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getTokenLargestAccounts",
                        "params": [token_address]
                    }
                    response_data = _helius_limiter.make_rpc_request(RPC_URL, payload, timeout=6)
                    if response_data and 'result' in response_data and 'value' in response_data['result']:
                        all_holders = response_data['result']['value']
                        if all_holders and supply > 0:
                                # Filter out bonding curve pools (>8% of supply) - EXACT simple_pump_analyzer logic
                                filtered_holders = []
                                for holder in all_holders:
                                    balance_raw = int(holder["amount"])
                                    decimals = int(holder.get("decimals", 6))
                                    balance_adjusted = balance_raw / (10 ** decimals)
                                    percent = (balance_adjusted / supply) * 100

                                    # Skip if this looks like a bonding curve pool (>8% of supply)
                                    if percent > 8.0:
                                        continue

                                    filtered_holders.append({
                                        'balance': balance_adjusted,
                                        'percent': percent
                                    })

                                # Take top 10 from filtered list and calculate percentage
                                top_10_holders = filtered_holders[:10]
                                top_10_percentage = sum(holder['percent'] for holder in top_10_holders)
                except Exception as e:
                    logger.debug(f"Could not get top 10 holder data: {e}")

                # Get token age and creation date
                token_age_minutes = 0
                token_age_formatted = "Unknown"
                creation_date = "Unknown"
                if dex_data.get('pair_created_at'):
                    import datetime
                    creation_time = dex_data['pair_created_at'] / 1000
                    current_time = time.time()
                    token_age_minutes = (current_time - creation_time) / 60
                    creation_date = datetime.datetime.fromtimestamp(creation_time).strftime("%Y-%m-%d %H:%M:%S")

                    # Format age for display
                    if token_age_minutes < 1:
                        token_age_formatted = f"{int(token_age_minutes * 60)} seconds old"
                    elif token_age_minutes < 60:
                        token_age_formatted = f"{int(token_age_minutes)} minutes old"
                    elif token_age_minutes < 1440:  # Less than 24 hours
                        hours = int(token_age_minutes / 60)
                        minutes = int(token_age_minutes % 60)
                        if hours == 1:
                            token_age_formatted = f"1 hour {minutes} minutes old"
                        else:
                            token_age_formatted = f"{hours} hours {minutes} minutes old"
                    else:  # 1+ days
                        days = int(token_age_minutes / 1440)
                        token_age_formatted = f"{days} days old"

                # Calculate analysis time
                analysis_time = time.time() - start_time
                logger.info(f"[FAST] Analysis completed in {analysis_time:.2f}s")

                # CRITICAL FIX: Check for high multipliers BEFORE accepting token
                prevent_high_multipliers = True  # Load from config later
                try:
                    # Load prevent_buy_if_x_multiplier setting from config
                    import json
                    with open('finalconfig.json', 'r') as f:
                        config_data = json.load(f)
                    # FIXED: Correct config path - it's under trading_settings
                    prevent_high_multipliers = config_data.get('trading_settings', {}).get('prevent_buy_if_x_multiplier', True)
                except Exception as e:
                    logger.warning(f"Could not load prevent_buy_if_x_multiplier setting: {e}")
                    prevent_high_multipliers = True  # Default to preventing high multipliers

                # CRITICAL FIX: Check if this is from GMGN channel - allow high multipliers for GMGN
                is_gmgn_signal = kwargs.get('is_gmgn_channel', False)

                # DEBUG: Log the signal source details
                is_pumpfun_volume_alert = kwargs.get('is_pumpfun_volume_alert', False)
                is_solbix_channel = kwargs.get('source_channel_id') == -1002428819353 or 'Solbix Community Calls' in str(kwargs.get('source_channel', ''))
                logger.info(f"🔍 SIGNAL SOURCE DEBUG for {token_address}:")
                logger.info(f"🔍   is_gmgn_channel: {is_gmgn_signal}")
                logger.info(f"🔍   is_pumpfun_volume_alert: {is_pumpfun_volume_alert}")
                logger.info(f"🔍   is_solbix_channel: {is_solbix_channel}")
                logger.info(f"🔍   source_channel_id: {kwargs.get('source_channel_id', 'NOT_SET')}")
                logger.info(f"🔍   source_channel: {kwargs.get('source_channel', 'NOT_SET')}")
                logger.info(f"🔍   source_type: {kwargs.get('source_type', 'NOT_SET')}")
                logger.info(f"🔍   All kwargs keys: {list(kwargs.keys())}")

                if prevent_high_multipliers:
                    logger.info(f"🔍 MULTIPLIER CHECK ENABLED: Checking {token_address} for high multipliers (3x+)")

                    # Check if this is a GMGN signal, Pumpfun Volume Alert signal, or Solbix signal
                    if is_gmgn_signal:
                        logger.info(f"🎯 GMGN SIGNAL DETECTED: Allowing high multipliers for {token_address}")
                        logger.info(f"🎯 GMGN signals are exempt from multiplier restrictions")
                    elif is_pumpfun_volume_alert:
                        logger.info(f"🎯 PUMPFUN VOLUME ALERT DETECTED: Allowing high multipliers for {token_address}")
                        logger.info(f"🎯 Pumpfun Volume Alert signals are exempt from multiplier restrictions")
                    elif is_solbix_channel:
                        logger.info(f"🎯 SOLBIX COMMUNITY CALLS DETECTED: Allowing high multipliers for {token_address}")
                        logger.info(f"🎯 Solbix Community Calls signals are exempt from multiplier restrictions")
                    else:
                        # Check price changes for high multipliers (non-GMGN signals only)
                        price_change_5m = dex_data.get('price_change_m5', 0) or 0
                        price_change_1h = dex_data.get('price_change_h1', 0) or 0
                        price_change_6h = dex_data.get('price_change_h6', 0) or 0
                        price_change_24h = dex_data.get('price_change_h24', 0) or 0

                        # Convert to absolute values for checking
                        max_price_change = max(abs(price_change_5m), abs(price_change_1h), abs(price_change_6h), abs(price_change_24h))

                        logger.info(f"📊 PRICE CHANGES: 5m: {price_change_5m:.1f}%, 1h: {price_change_1h:.1f}%, 6h: {price_change_6h:.1f}%, 24h: {price_change_24h:.1f}% | Max: {max_price_change:.1f}%")

                        # REJECT tokens with 5x+ multipliers (400%+ gains) - Allow more room for early entries
                        if max_price_change >= 400:  # 400% = 5x multiplier
                            logger.warning(f"🚫 REJECTING HIGH MULTIPLIER TOKEN: {token_address}")
                            logger.warning(f"   Max price change: {max_price_change:.1f}% (>= 400% = 5x)")
                            logger.warning(f"   REASON: Only buying coins with maximum 5x multiplier")
                            logger.warning(f"   NOTE: GMGN, Pumpfun Volume Alert, and Solbix Community Calls signals are exempt from this restriction")
                            return {
                                "exists": False,
                                "reason": f"High multiplier token rejected: {max_price_change:.1f}% gain (>= 5x). Only buying coins with max 5x. (GMGN, Pumpfun Volume Alert, and Solbix Community Calls signals exempt)",
                                "price_change_5m": price_change_5m,
                                "price_change_1h": price_change_1h,
                                "price_change_6h": price_change_6h,
                                "price_change_24h": price_change_24h,
                                "max_price_change": max_price_change
                            }
                        elif max_price_change >= 200:  # 200% = 3x multiplier (warn but allow)
                            logger.info(f"⚠️ 3-4X MULTIPLIER TOKEN: {token_address} - {max_price_change:.1f}% gain (allowing as it's <= 5x)")
                        elif max_price_change >= 100:  # 100% = 2x multiplier (good)
                            logger.info(f"✅ 2X MULTIPLIER TOKEN: {token_address} - {max_price_change:.1f}% gain (good entry)")
                        else:
                            logger.info(f"✅ NEW/LOW MULTIPLIER TOKEN: {token_address} - {max_price_change:.1f}% gain (excellent entry)")
                else:
                    logger.info(f"⚠️ MULTIPLIER CHECK DISABLED: Allowing {token_address} without checking multipliers")

                # BULLETPROOF: Return format with normalized liquidity data to prevent conflicts
                result = {
                    "exists": True,
                    "price": price_usd,
                    "price_source": "dexscreener",
                    "market_cap": market_cap,
                    "liquidity_source": liquidity_source,
                    "show_multiplier": show_multiplier,
                    "pool_sol": pool_sol,  # Add pool SOL balance for debugging
                    "holder_count": holders,
                    "top_10_percentage": top_10_percentage,  # Top 10 holders percentage
                    "token_age_minutes": token_age_minutes,  # Token age in minutes
                    "pair_age_minutes": int(token_age_minutes),  # Format as integer for display consistency
                    "creation_date": creation_date,  # Human readable creation date
                    "volume_24h": dex_data.get('volume24h', 0),
                    "volume_5m": dex_data.get('volume5m', 0),
                    # BULLETPROOF: Add ALL possible liquidity field names to prevent conflicts
                    "liquidity": liquidity_value,
                    "liquidity_usd": liquidity_value,
                    "liquidity_value": liquidity_value,
                    "usd_liquidity": liquidity_value,
                    "liquidity_sol": liquidity_value / sol_price if sol_price > 0 else 0,
                    "raw_liquidity": dex_liquidity_raw if 'dex_liquidity_raw' in locals() else liquidity_value,
                    # Add price change data for debugging
                    "price_change_5m": dex_data.get('price_change_m5', 0),
                    "price_change_1h": dex_data.get('price_change_h1', 0),
                    "price_change_6h": dex_data.get('price_change_h6', 0),
                    "price_change_24h": dex_data.get('price_change_h24', 0),
                    "volume_1h": dex_data.get('volume_h1', 0),
                    "volume_6h": dex_data.get('volume_h6', 0),
                    "fdv": dex_data.get('market_cap', market_cap),
                    "token_age": token_age_formatted,  # Use formatted age instead of raw timestamp
                    "name": dex_data.get('name', 'Unknown'),
                    "symbol": dex_data.get('symbol', 'Unknown'),
                    "confidence": 0.8,  # High confidence for pump.fun tokens
                    "analysis_time": analysis_time,
                    # Add DexScreener specific fields for display
                    "dex_id": dex_data.get('dex_id', 'Unknown'),
                    "pair_address": dex_data.get('pair_address', 'Unknown'),
                    "pair_created_at": dex_data.get('pair_created_at', 0),
                    # Add detailed transaction data (EXACT original structure)
                    "txns_5m_buys": dex_data.get('txns_m5', {}).get('buys', 0),
                    "txns_5m_sells": dex_data.get('txns_m5', {}).get('sells', 0),
                    "txns_1h_buys": dex_data.get('txns_h1', {}).get('buys', 0),
                    "txns_1h_sells": dex_data.get('txns_h1', {}).get('sells', 0),
                    "txns_6h_buys": dex_data.get('txns_h6', {}).get('buys', 0),
                    "txns_6h_sells": dex_data.get('txns_h6', {}).get('sells', 0),
                    "txns_24h_buys": dex_data.get('txns_h24', {}).get('buys', 0),
                    "txns_24h_sells": dex_data.get('txns_h24', {}).get('sells', 0),
                    # Add transaction count for 1h (for notifications)
                    "tx_count_1h": dex_data.get('txns_h1', {}).get('buys', 0) + dex_data.get('txns_h1', {}).get('sells', 0),
                    # Add price change data
                    "price_change_5m": dex_data.get('price_change_m5', 0),
                    "price_change_1h": dex_data.get('price_change_h1', 0),
                    "price_change_6h": dex_data.get('price_change_h6', 0),
                    "price_change_24h": dex_data.get('price_change_h24', 0),
                    "raw_data": {"dexscreener": dex_data}
                }

                # Add signal-specific data if provided
                if 'signal_message' in kwargs:
                    result['signal_message'] = kwargs['signal_message']
                if 'is_gmgn_channel' in kwargs:
                    result['is_gmgn_channel'] = kwargs['is_gmgn_channel']

                # ========================================
                # INTEGRATED RUG PROTECTION CHECKS
                # ========================================
                # CRITICAL FIX: Skip rug protection for manual analysis (option 21)
                bypass_rug_protection = kwargs.get('bypass_rug_protection', False)
                logger.info(f"[DEBUG] RUG PROTECTION BYPASS CHECK: bypass_rug_protection={bypass_rug_protection}, kwargs={kwargs}")

                if not bypass_rug_protection:
                    # Use existing rug protection system from token_analyzer.py
                    # Create a minimal data structure for rug protection validation
                    rug_data = {
                        'liquidity_usd': liquidity_value,
                        'top10HolderPercent': top_10_percentage,
                        'isBlacklisted': dex_data.get('isBlacklisted', False) if dex_data else False,
                        'volume_m5': dex_data.get('volume5m', 0) if dex_data else 0,
                        'fdv': market_cap,  # Use market cap as FDV approximation
                        'devSold': dex_data.get('devSold', False) if dex_data else False
                    }

                    # Import and use existing rug protection logic
                    try:
                        from config_manager import ConfigManager
                        temp_config = ConfigManager()
                        trading_settings = temp_config.get_section('trading_settings')
                        rug_config = trading_settings.get('rug_protection', {})

                        if rug_config.get('enabled', True):
                            logger.info(f"[SHIELD] EXECUTING RUG PROTECTION CHECKS for {token_address}")

                            # RUG CHECK 1: Liquidity Protection
                            min_liquidity = rug_config.get('min_liquidity_usd', 8000.0)  # SURGICAL FIX: Updated default
                            if liquidity_value < min_liquidity:
                                logger.warning(f"[ALERT] RUG PROTECTION TRIGGERED: Low liquidity ${liquidity_value:,.0f} < ${min_liquidity:,.0f}")
                                result['exists'] = False
                                result['reason'] = f"[ALERT] RUG RISK: Low liquidity ${liquidity_value:,.0f} < ${min_liquidity:,.0f} (easy to rug)"
                                result['rug_risk'] = "HIGH_LIQUIDITY_RISK"
                                return result

                        # RUG CHECK 2: Whale Concentration Protection
                        max_whale_pct = rug_config.get('max_top10_holders_percent', 30.0)
                        if top_10_percentage > max_whale_pct:
                            logger.warning(f"[ALERT] RUG PROTECTION TRIGGERED: High whale concentration {top_10_percentage:.1f}% > {max_whale_pct:.1f}%")
                            result['exists'] = False
                            result['reason'] = f"[ALERT] RUG RISK: High whale concentration {top_10_percentage:.1f}% > {max_whale_pct:.1f}% (whale rug risk)"
                            result['rug_risk'] = "HIGH_WHALE_RISK"
                            return result

                        # RUG CHECK 3: Market Cap Protection
                        min_market_cap = rug_config.get('min_market_cap_usd', 10000.0)
                        if market_cap < min_market_cap:
                            logger.warning(f"[ALERT] RUG PROTECTION TRIGGERED: Low market cap ${market_cap:,.0f} < ${min_market_cap:,.0f}")
                            result['exists'] = False
                            result['reason'] = f"[ALERT] RUG RISK: Low market cap ${market_cap:,.0f} < ${min_market_cap:,.0f} (too small/risky)"
                            result['rug_risk'] = "LOW_MARKET_CAP"
                            return result

                        # RUG CHECK 4: Blacklist Protection
                        if rug_config.get('check_blacklist', True):
                            is_blacklisted = rug_data.get('isBlacklisted', False)
                            if is_blacklisted:
                                logger.warning(f"[ALERT] RUG PROTECTION TRIGGERED: Token is blacklisted")
                                result['exists'] = False
                                result['reason'] = f"[ALERT] RUG RISK: Token is blacklisted (known scam/rug)"
                                result['rug_risk'] = "BLACKLISTED"
                                return result

                            logger.info(f"[SUCCESS] RUG PROTECTION: All checks passed - Liq: ${liquidity_value:,.0f}, MC: ${market_cap:,.0f}, Whale: {top_10_percentage:.1f}%")

                    except Exception as e:
                        logger.error(f"Error in rug protection checks: {e}")
                        # Continue without rug protection if there's an error
                else:
                    logger.info(f"[BYPASS] RUG PROTECTION BYPASSED for manual analysis")

                logger.info(f"[SUCCESS] Fast analysis result: Price=${price_usd:.8f}, MC=${market_cap:,.0f}, Liq=${liquidity_value:,.0f}")

                # BULLETPROOF: Mark token as permanently processed
                try:
                    if result.get('exists', False):
                        # Token passed analysis - will be bought
                        mark_token_permanent_decision(token_address, "analyzed", "Passed analysis")
                    else:
                        # Token failed analysis - permanently skip
                        skip_reason = result.get('reason', 'Failed analysis')
                        mark_token_permanent_decision(token_address, "skipped", skip_reason)

                    logger.debug(f"Permanent decision recorded for {token_address}")
                except Exception as e:
                    logger.warning(f"Failed to record permanent decision for {token_address}: {e}")

                return result

            except Exception as e:
                logger.error(f"[ERROR] Error in fast token analysis: {e}")
                return {"exists": False, "reason": f"Analysis error: {str(e)}"}
            finally:
                # SURGICAL FIX: Always remove from active set
                async with _analysis_lock:
                    _active_analyses.discard(token_address)

        async def monitor_position_only(token_address: str) -> Dict[str, Any]:
            """
            BULLETPROOF POSITION MONITORING: Lightweight price/liquidity updates with concurrency protection
            NO full analysis, NO rug protection, NO signal processing
            """
            try:
                logger.debug(f"POSITION MONITORING: Getting price update for {token_address}")

                # BULLETPROOF: Get token-specific lock to prevent concurrent data access
                token_lock = await get_token_data_lock(token_address)

                # BULLETPROOF: Lightweight DexScreener call with token-level locking
                async with token_lock:
                    dex_data = get_dexscreener_data(token_address, bypass_rate_limit=True)

                if dex_data:
                    result = {
                        "exists": True,
                        "price": dex_data.get('price', 0),
                        "liquidity_usd": dex_data.get('liquidity', 0),
                        "market_cap": dex_data.get('marketCap', 0),
                        "volume_24h": dex_data.get('volume24h', 0),
                        "volume_5m": dex_data.get('volume5m', 0),
                        "monitoring_only": True  # Flag to indicate this is position monitoring
                    }
                    logger.debug(f"POSITION MONITORING: {token_address} - Price: ${result['price']:.8f}, Liquidity: ${result['liquidity_usd']:,.0f}")
                    return result
                else:
                    logger.warning(f"POSITION MONITORING: No data for {token_address}")
                    return {"exists": False, "monitoring_only": True}

            except Exception as e:
                logger.warning(f"POSITION MONITORING: Error for {token_address}: {e}")
                return {"exists": False, "monitoring_only": True, "error": str(e)}

        # BULLETPROOF: Set both functions for different purposes
        global _fast_analysis_function, _position_monitoring_function
        _fast_analysis_function = analyze_new_signal_only  # For NEW signals only
        _position_monitoring_function = monitor_position_only  # For bought positions only
        SIMPLE_PUMP_AVAILABLE = True
        # PRODUCTION VALIDATION: Will be done in main() function

        # UNIFIED FIX: Make get_dexscreener_data globally accessible (DISABLED to prevent event loop conflicts)
        # global get_dexscreener_data_global
        # get_dexscreener_data_global = get_dexscreener_data

        logger.info("[SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized")
        logger.info(f"[SUCCESS] PERMANENT TRACKING: {len(PERMANENTLY_ANALYZED_TOKENS)} analyzed, {len(PERMANENTLY_SKIPPED_TOKENS)} skipped")

    except ImportError as e:
        SIMPLE_PUMP_AVAILABLE = False
        _fast_analysis_function = None
        _position_monitoring_function = None
        logger.error(f"[ERROR] Failed to import simple_pump_analyzer: {e}")
        logger.error(f"[ERROR] ImportError details: {traceback.format_exc()}")
        logger.info("Will use standard token_analyzer as fallback")
    except Exception as e:
        SIMPLE_PUMP_AVAILABLE = False
        _fast_analysis_function = None
        _position_monitoring_function = None
        logger.error(f"[ERROR] Failed to initialize simple_pump_analyzer: {e}")
        logger.error(f"[ERROR] Exception details: {traceback.format_exc()}")
        logger.info("Will use standard token_analyzer as fallback")

# Force UTF-8 encoding for Windows console - simplified from independent_signal_bot.py
if os.name == "nt":  # Only apply on Windows
    try:
        # First try to enable UTF-8 mode for the console
        import ctypes
        kernel32 = ctypes.windll.kernel32
        kernel32.SetConsoleOutputCP(65001)  # Set console output to UTF-8
        kernel32.SetConsoleCP(65001)  # Set console input to UTF-8

        # OPERATOR FIX: Unicode-safe logging configuration
        try:
            from unicode_safe_logging import setup_unicode_safe_logging, configure_windows_console
            configure_windows_console()
            setup_unicode_safe_logging()
            logger.info("Successfully configured Unicode-safe logging")
        except Exception as e:
            logger.warning(f"Could not configure Unicode-safe logging: {e}")
            # Fallback to basic configuration
            import codecs
            if hasattr(sys.stdout, 'buffer'):
                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, errors='replace')
            if hasattr(sys.stderr, 'buffer'):
                sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, errors='replace')
            logger.info("Successfully configured basic console encoding with error handling")

    except Exception as e:
        logger.info(f"Console encoding setup skipped (not needed): {e}")

async def main():
    logger.info("Starting bot...")
    print("Starting bot...")

    try:
        # Initialize simple pump analyzer for fast token analysis
        init_simple_pump_analyzer()

        # Initialize configuration and state managers
        config_manager = ConfigManager()

        # OPERATOR FIX: Basic validation only (removed production lock)
        try:
            validation_result = config_manager.validate_config()
            if validation_result.get('is_valid', True):
                print("\n[SUCCESS] Configuration validation passed!")
            else:
                print("\n[WARNING] Configuration issues detected:")
                for issue in validation_result.get('critical_issues', []):
                    print(f"  • {issue}")
                print("Bot will continue - user can choose mode manually.")

        except Exception as e:
            logger.warning(f"Configuration validation failed: {e}")
            print("[WARNING] Using basic validation...")

        state_manager = StateManager(config_manager)

        # Initialize bot controller
        bot_controller = BotController(config_manager, state_manager)

        # CRITICAL FIX: Set trader instance in state manager for balance synchronization
        state_manager.trader_instance = bot_controller.pumpportal_trader
        logger.info("✅ Trader instance set in state manager for balance synchronization")

        # BULLETPROOF: Set both analysis and monitoring functions
        logger.info(f"CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = {SIMPLE_PUMP_AVAILABLE}")
        logger.info(f"CRITICAL DEBUG: _fast_analysis_function = {_fast_analysis_function}")
        logger.info(f"CRITICAL DEBUG: _position_monitoring_function = {_position_monitoring_function}")

        if SIMPLE_PUMP_AVAILABLE and _fast_analysis_function:
            bot_controller.set_fast_analysis_function(_fast_analysis_function)
            logger.info("[SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)")
        else:
            logger.error(f"CRITICAL ERROR: Fast analysis function NOT SET! SIMPLE_PUMP_AVAILABLE={SIMPLE_PUMP_AVAILABLE}, _fast_analysis_function={_fast_analysis_function}")

        if SIMPLE_PUMP_AVAILABLE and _position_monitoring_function:
            bot_controller.set_position_monitoring_function(_position_monitoring_function)
            logger.info("[SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)")
        else:
            logger.error(f"CRITICAL ERROR: Position monitoring function NOT SET! SIMPLE_PUMP_AVAILABLE={SIMPLE_PUMP_AVAILABLE}, _position_monitoring_function={_position_monitoring_function}")

        # Log multiplier filtering status
        try:
            import json
            with open('finalconfig.json', 'r') as f:
                config_data = json.load(f)
            prevent_multipliers = config_data.get('trading_settings', {}).get('prevent_buy_if_x_multiplier', True)
            if prevent_multipliers:
                logger.info("🎯 MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)")
                print("🎯 MULTIPLIER FILTERING: ENABLED - Allowing tokens up to 5x multiplier")
            else:
                logger.warning("⚠️ MULTIPLIER FILTERING: DISABLED - Will buy tokens regardless of multipliers")
                print("⚠️ MULTIPLIER FILTERING: DISABLED - Will buy tokens regardless of multipliers")
        except Exception as e:
            logger.warning(f"Could not check multiplier filtering status: {e}")

        # Initialize token_analyzer with dexscreener functionality
        logger.info("Initializing DEX adapters and sessions")
        try:
            await bot_controller.token_analyzer.init_sessions()
            bot_controller.token_analyzer.dex_sessions_initialized = True
            logger.info("DEX sessions initialized successfully")
            logger.info("LLM integration is not available in this configuration")
        except Exception as e:
            logger.error(f"Error initializing DEX sessions: {e}")
            logger.error(traceback.format_exc())

        # Setup direct signal handling from independent_signal_bot
        if bot_controller.signal_handler:
            # Register direct signal callback to ensure signals are quickly processed
            bot_controller.signal_handler.register_direct_signal_callback(bot_controller.handle_new_signal)
            logger.info("Registered direct signal callback for faster signal proyescessing")

        # Phi-2 LLM integration has been removed
        logger.info("LLM integration is not available in this configuration")

        # Display parallel pipeline architecture information
        print("\n=== Parallel Pipeline Architecture ===")
        print("This bot uses a parallel pipeline architecture for high performance:")
        print("1. Signal Intake Queue: Optimized for non-blocking signal processing")
        print("2. Token Analysis Queue: Parallel worker pool for token analysis")
        print("3. Trade Execution Queue: Priority-based execution with sell priority")

        # Display worker counts (optimized for performance)
        analysis_workers = config_manager.get('trading_settings', 'num_analysis_workers', default=8)
        execution_workers = config_manager.get('trading_settings', 'num_execution_workers', default=4)
        print(f"Analysis Workers: {analysis_workers}")
        print(f"Execution Workers: {execution_workers}")

        # Display performance optimizations
        print("\n=== Performance Optimizations ===")
        print("1. Parallel API Calls: Multiple data sources queried simultaneously")
        print("2. Ultra-fast Timeouts: 1.0s max for meme coin speed")
        print("3. High Concurrency: Processing multiple tokens in parallel")
        print("4. Optimized Rate Limiting: 250 RPM DexScreener API usage (enhanced reliability)")
        print("\nUse option 31 in the menu to view pipeline status\n")

        # Initialize CLI interface
        cli = CLIInterface(bot_controller)

        # SURGICAL FIX: Initialize bot controller (position monitoring only, no auto-trading)
        try:
            logger.info("Initializing bot controller WITHOUT signal processing...")
            await bot_controller.initialize(enable_signal_processing=False)  # Initialize WITHOUT signal processing
            logger.info("[SUCCESS] Bot controller initialized successfully WITHOUT signal processing")
        except Exception as e:
            logger.error(f"Failed to initialize bot controller: {e}")
            raise

        # SURGICAL FIX: Start cache cleanup task
        if SIMPLE_PUMP_AVAILABLE:
            asyncio.create_task(cache_cleanup_task())
            logger.info("Started cache cleanup task")

        # OPERATOR FIX: Start production monitoring
        try:
            from production_monitor import start_production_monitoring
            start_production_monitoring(bot_controller)
            logger.info("[SUCCESS] Production monitoring started")  # Removed emoji for Windows compatibility
        except Exception as e:
            logger.warning(f"Production monitoring failed to start: {e}")

        # OPERATOR FIX: Setup error tracking
        try:
            from error_tracker import setup_error_tracking
            setup_error_tracking()
            logger.info("[SUCCESS] Error tracking enabled")  # Removed emoji for Windows compatibility
        except Exception as e:
            logger.warning(f"Error tracking failed to start: {e}")

        # OPERATOR FIX: Setup trading metrics
        try:
            from trading_metrics import get_trading_metrics_collector
            trading_metrics = get_trading_metrics_collector()
            logger.info("[SUCCESS] Trading metrics collector enabled")  # Removed emoji for Windows compatibility
        except Exception as e:
            logger.warning(f"Trading metrics failed to start: {e}")

        # LLM integration has been removed

        try:
            # Start the CLI interface
            logger.info("Starting CLI interface...")
            await cli.start()
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
            print("\nBot stopped by user")
        except Exception as e:
            logger.error(f"Error running bot: {e}")
            logger.error(traceback.format_exc())
            print(f"Error running bot: {e}")
        finally:
            # Cleanup
            logger.info("Cleaning up resources...")
            await bot_controller.cleanup()

            # SURGICAL FIX: Close global analysis session to prevent memory leak
            global _analysis_session
            if _analysis_session:
                try:
                    _analysis_session.close()
                    logger.info("Closed global analysis session")
                except Exception as e:
                    logger.warning(f"Error closing analysis session: {e}")

    except Exception as e:
        logger.critical(f"Critical error during bot initialization: {e}")
        logger.critical(traceback.format_exc())
        print(f"Critical error during bot initialization: {e}")
        print("Check the log file for details.")

    logger.info("Bot shutdown complete.")
    print("Bot shutdown complete.")

# OPTIMIZED: Removed duplicate functions - using centralized versions from init_simple_pump_analyzer()
# The should_analyze_token, get_cached_analysis, and cache_token_analysis functions
# are now defined once inside init_simple_pump_analyzer() to eliminate duplication

# SURGICAL FIX: Global cache function for external imports
cache_token_analysis_global = None

# UNIFIED FIX: Global DexScreener function for external imports
get_dexscreener_data_global = None

def cache_token_analysis(token_address, result, reason=None):
    """Global wrapper for cache_token_analysis function with enhanced error handling"""
    global cache_token_analysis_global
    try:
        if cache_token_analysis_global and callable(cache_token_analysis_global):
            return cache_token_analysis_global(token_address, result, reason)
        else:
            # Silently skip caching if not initialized to avoid log spam
            pass
    except Exception as e:
        logger.debug(f"Cache function error for {token_address}: {e}")

def get_dexscreener_data(token_address, bypass_rate_limit=False):
    """Global wrapper for get_dexscreener_data function - EMERGENCY FALLBACK"""
    global get_dexscreener_data_global
    try:
        if get_dexscreener_data_global and callable(get_dexscreener_data_global):
            return get_dexscreener_data_global(token_address, bypass_rate_limit)
        else:
            # EMERGENCY FALLBACK: Direct API call to prevent bot breakage
            logger.warning(f"Using emergency fallback for {token_address}")
            try:
                import requests
                url = f"https://api.dexscreener.com/token-pairs/v1/solana/{token_address}"
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    pairs = data if isinstance(data, list) else data.get('pairs', [])
                    if pairs:
                        best_pair = pairs[0]
                        base_token = best_pair.get('baseToken', {})
                        return {
                            'exists': True,
                            'name': base_token.get('name', 'Unknown'),
                            'symbol': base_token.get('symbol', 'Unknown'),
                            'price': float(best_pair.get('priceUsd', 0) or 0),
                            'liquidity': float(best_pair.get('liquidity', {}).get('usd', 0) or 0),
                            'volume24h': float(best_pair.get('volume', {}).get('h24', 0) or 0),
                            'source': 'DexScreener-Fallback'
                        }
                return {"exists": False, "error": "Emergency fallback failed"}
            except Exception as fallback_error:
                return {"exists": False, "error": f"All methods failed: {str(fallback_error)}"}
    except Exception as e:
        logger.error(f"Error in global get_dexscreener_data wrapper: {e}")
        return {"exists": False, "error": f"DexScreener wrapper error: {str(e)}"}

async def cache_cleanup_task():
    """Background task to clean up stale cache entries"""
    import time  # Import time module for this function
    while True:
        try:
            await asyncio.sleep(900)  # 15 minutes (reduced from 30 minutes)
            current_time = time.time()
            expired_tokens = []

            global _token_analysis_cache
            for token, data in _token_analysis_cache.items():
                age = current_time - data["timestamp"]
                # Remove entries older than 1 hour (reduced from 2 hours)
                if age > 3600:
                    expired_tokens.append(token)

            for token in expired_tokens:
                del _token_analysis_cache[token]

            if expired_tokens:
                logger.info(f"CACHE CLEANUP: Removed {len(expired_tokens)} expired entries")

        except Exception as e:
            logger.error(f"Cache cleanup error: {e}")

if __name__ == "__main__":
    try:
        # FIXED: Only show menu, don't auto-start the bot
        from cli_interface import show_main_menu
        show_main_menu()
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        logger.critical(f"Unhandled exception in main: {e}")
        logger.critical(traceback.format_exc())
        print(f"Unhandled exception: {e}")
        print("Check the log file for details.")

# SURGICAL FIX: Global functions for bot_controller imports
should_analyze_token_permanent_global = None
mark_token_permanent_decision_global = None

def should_analyze_token_permanent(token_address):
    """Global wrapper for should_analyze_token_permanent function"""
    global should_analyze_token_permanent_global
    try:
        if should_analyze_token_permanent_global and callable(should_analyze_token_permanent_global):
            return should_analyze_token_permanent_global(token_address)
        else:
            logger.debug(f"should_analyze_token_permanent_global not available for {token_address}")
            return True, "Function not available - allowing analysis"
    except Exception as e:
        logger.error(f"Error in global should_analyze_token_permanent wrapper: {e}")
        return True, "Error in function - allowing analysis"

def mark_token_permanent_decision(token_address, decision, reason=None):
    """Global wrapper for mark_token_permanent_decision function"""
    global mark_token_permanent_decision_global
    try:
        if mark_token_permanent_decision_global and callable(mark_token_permanent_decision_global):
            mark_token_permanent_decision_global(token_address, decision, reason)
        else:
            logger.debug(f"mark_token_permanent_decision_global not available for {token_address}")
    except Exception as e:
        logger.error(f"Error in global mark_token_permanent_decision wrapper: {e}")
