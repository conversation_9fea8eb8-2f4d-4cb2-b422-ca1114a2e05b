2025-06-21 18:17:28,575 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 18:17:28,576 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 18:17:28,576 - signal_handler - INFO - Loaded session string from C:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 18:17:28,576 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750510048
2025-06-21 18:17:28,576 - signal_handler - INFO - Using session path: C:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750510048
2025-06-21 18:17:28,576 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 18:17:28,584 - signal_handler - INFO - Direct signal callback registered
2025-06-21 18:17:28,585 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 18:17:28,585 - signal_handler - INFO - API ID loaded: True
2025-06-21 18:17:28,585 - signal_handler - INFO - API Hash loaded: True
2025-06-21 18:17:28,585 - signal_handler - INFO - Phone number loaded: True
2025-06-21 18:17:28,585 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 18:17:28,585 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 18:17:28,585 - signal_handler - INFO - Using saved session string
2025-06-21 18:17:28,586 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 18:17:28,891 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:17:28,939 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:17:28,939 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 18:17:28,940 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:17:28,989 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:17:28,990 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 18:17:28,990 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:17:29,041 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:17:29,041 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 18:17:29,041 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 18:17:29,041 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 18:17:29,169 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 18:17:29,170 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 18:17:29,170 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 18:17:29,170 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 18:17:29,171 - signal_handler - INFO - Direct signal callback registered
2025-06-21 18:17:29,618 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 18:17:30,016 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 18:17:33,045 - signal_handler - INFO - [msg_1750510053043_3732] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 18:17:33

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 18:17:33,045 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 18:17:33,045 - signal_handler - INFO - [msg_1750510053043_3732] Message queued with normal priority
2025-06-21 18:17:33,065 - signal_handler - INFO - [msg_1750510053065_6499] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 18:17:33
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 18:17:33,066 - signal_handler - INFO - [msg_1750510053065_6499] Message queued with normal priority
2025-06-21 18:17:33,431 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 18:17:33,431 - signal_handler - INFO - [msg_1750510053043_3732] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:17:33,479 - signal_handler - INFO - [msg_1750510053043_3732] Successfully resolved info channel entity: Bot Info
2025-06-21 18:17:33,615 - signal_handler - INFO - [msg_1750510053043_3732] Message sent directly successfully using entity
2025-06-21 18:17:33,615 - signal_handler - INFO - [msg_1750510053043_3732] Message sent successfully via direct send method
2025-06-21 18:17:33,615 - signal_handler - INFO - [msg_1750510053043_3732] Message sent successfully on attempt 1
2025-06-21 18:17:33,615 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 18:17:34,123 - signal_handler - INFO - [msg_1750510053065_6499] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:17:34,173 - signal_handler - INFO - [msg_1750510053065_6499] Successfully resolved info channel entity: Bot Info
2025-06-21 18:17:34,260 - signal_handler - INFO - [msg_1750510053065_6499] Message sent directly successfully using entity
2025-06-21 18:17:34,260 - signal_handler - INFO - [msg_1750510053065_6499] Message sent successfully via direct send method
2025-06-21 18:17:34,260 - signal_handler - INFO - [msg_1750510053065_6499] Message sent successfully on attempt 1
2025-06-21 18:17:41,766 - signal_handler - DEBUG - Signal processing DISABLED - ignoring message: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge A...
2025-06-21 18:17:47,430 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 18:17:47,430 - signal_handler - INFO - API ID loaded: True
2025-06-21 18:17:47,431 - signal_handler - INFO - API Hash loaded: True
2025-06-21 18:17:47,431 - signal_handler - INFO - Phone number loaded: True
2025-06-21 18:17:47,431 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 18:17:47,431 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 18:17:47,431 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:17:47,479 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:17:47,479 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 18:17:47,479 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:17:47,528 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:17:47,528 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 18:17:47,528 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:17:47,575 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:17:47,575 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 18:17:47,575 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 18:17:47,575 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 18:17:47,653 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 18:17:47,655 - signal_handler - INFO - [msg_1750510067654_6194] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 18:17:47
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 18:17:47,655 - signal_handler - INFO - [msg_1750510067654_6194] Message queued with normal priority
2025-06-21 18:17:47,749 - signal_handler - INFO - [msg_1750510067654_6194] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:17:47,804 - signal_handler - INFO - [msg_1750510067654_6194] Successfully resolved info channel entity: Bot Info
2025-06-21 18:17:47,885 - signal_handler - INFO - [msg_1750510067654_6194] Message sent directly successfully using entity
2025-06-21 18:17:47,885 - signal_handler - INFO - [msg_1750510067654_6194] Message sent successfully via direct send method
2025-06-21 18:17:47,885 - signal_handler - INFO - [msg_1750510067654_6194] Message sent successfully on attempt 1
2025-06-21 18:17:48,155 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 18:17:48,156 - signal_handler - INFO - Direct signal callback registered
2025-06-21 18:17:48,156 - signal_handler - INFO - [msg_1750510068156_2849] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capital: 0.0...
2025-06-21 18:17:48,157 - signal_handler - INFO - [msg_1750510068156_2849] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:17:48,208 - signal_handler - INFO - [msg_1750510068156_2849] Successfully resolved info channel entity: Bot Info
2025-06-21 18:17:48,281 - signal_handler - INFO - [msg_1750510068156_2849] Message sent directly successfully using entity
2025-06-21 18:17:48,282 - signal_handler - INFO - [msg_1750510068156_2849] Message sent directly successfully
