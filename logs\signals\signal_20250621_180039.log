2025-06-21 18:00:39,901 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 18:00:39,901 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 18:00:39,901 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 18:00:39,901 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750509039
2025-06-21 18:00:39,901 - signal_handler - INFO - Using session path: c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750509039
2025-06-21 18:00:39,902 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 18:00:39,908 - signal_handler - INFO - Direct signal callback registered
2025-06-21 18:00:39,910 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 18:00:39,910 - signal_handler - INFO - API ID loaded: True
2025-06-21 18:00:39,910 - signal_handler - INFO - API Hash loaded: True
2025-06-21 18:00:39,910 - signal_handler - INFO - Phone number loaded: True
2025-06-21 18:00:39,910 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 18:00:39,910 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 18:00:39,911 - signal_handler - INFO - Using saved session string
2025-06-21 18:00:39,911 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 18:00:40,508 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:00:40,714 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:00:40,714 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 18:00:40,715 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:00:40,846 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:00:40,846 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 18:00:40,847 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:00:40,936 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:00:40,936 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 18:00:40,937 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 18:00:40,937 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 18:00:41,147 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 18:00:41,148 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 18:00:41,149 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 18:00:41,149 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 18:00:41,151 - signal_handler - INFO - Direct signal callback registered
2025-06-21 18:00:41,696 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 18:00:43,661 - signal_handler - INFO - [msg_1750509043659_2212] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 18:00:43

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 18:00:43,661 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 18:00:43,661 - signal_handler - INFO - [msg_1750509043659_2212] Message queued with normal priority
2025-06-21 18:00:43,681 - signal_handler - INFO - [msg_1750509043681_6064] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 18:00:43
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 18:00:43,681 - signal_handler - INFO - [msg_1750509043681_6064] Message queued with normal priority
2025-06-21 18:00:43,930 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 18:00:43,930 - signal_handler - INFO - [msg_1750509043659_2212] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:00:44,013 - signal_handler - INFO - [msg_1750509043659_2212] Successfully resolved info channel entity: Bot Info
2025-06-21 18:00:44,128 - signal_handler - INFO - [msg_1750509043659_2212] Message sent directly successfully using entity
2025-06-21 18:00:44,129 - signal_handler - INFO - [msg_1750509043659_2212] Message sent successfully via direct send method
2025-06-21 18:00:44,129 - signal_handler - INFO - [msg_1750509043659_2212] Message sent successfully on attempt 1
2025-06-21 18:00:44,129 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 18:00:44,638 - signal_handler - INFO - [msg_1750509043681_6064] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:00:44,719 - signal_handler - INFO - [msg_1750509043681_6064] Successfully resolved info channel entity: Bot Info
2025-06-21 18:00:44,833 - signal_handler - INFO - [msg_1750509043681_6064] Message sent directly successfully using entity
2025-06-21 18:00:44,833 - signal_handler - INFO - [msg_1750509043681_6064] Message sent successfully via direct send method
2025-06-21 18:00:44,833 - signal_handler - INFO - [msg_1750509043681_6064] Message sent successfully on attempt 1
2025-06-21 18:01:02,497 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 18:01:02,497 - signal_handler - INFO - API ID loaded: True
2025-06-21 18:01:02,497 - signal_handler - INFO - API Hash loaded: True
2025-06-21 18:01:02,497 - signal_handler - INFO - Phone number loaded: True
2025-06-21 18:01:02,497 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 18:01:02,497 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 18:01:02,498 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:01:02,681 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:01:02,682 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 18:01:02,682 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:01:02,864 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:01:02,865 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 18:01:02,865 - signal_handler - INFO - Checking authorization status...
2025-06-21 18:01:02,962 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 18:01:02,962 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 18:01:02,963 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 18:01:02,963 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 18:01:03,099 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 18:01:03,101 - signal_handler - INFO - [msg_1750509063101_7856] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 18:01:03
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 18:01:03,102 - signal_handler - INFO - [msg_1750509063101_7856] Message queued with normal priority
2025-06-21 18:01:03,153 - signal_handler - INFO - [msg_1750509063101_7856] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:01:03,238 - signal_handler - INFO - [msg_1750509063101_7856] Successfully resolved info channel entity: Bot Info
2025-06-21 18:01:03,354 - signal_handler - INFO - [msg_1750509063101_7856] Message sent directly successfully using entity
2025-06-21 18:01:03,355 - signal_handler - INFO - [msg_1750509063101_7856] Message sent successfully via direct send method
2025-06-21 18:01:03,356 - signal_handler - INFO - [msg_1750509063101_7856] Message sent successfully on attempt 1
2025-06-21 18:01:03,604 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 18:01:03,605 - signal_handler - INFO - Direct signal callback registered
2025-06-21 18:01:03,605 - signal_handler - INFO - [msg_1750509063605_7391] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capital: 0.0...
2025-06-21 18:01:03,606 - signal_handler - INFO - [msg_1750509063605_7391] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:01:03,687 - signal_handler - INFO - [msg_1750509063605_7391] Successfully resolved info channel entity: Bot Info
2025-06-21 18:01:03,794 - signal_handler - INFO - [msg_1750509063605_7391] Message sent directly successfully using entity
2025-06-21 18:01:03,794 - signal_handler - INFO - [msg_1750509063605_7391] Message sent directly successfully
2025-06-21 18:01:17,508 - signal_handler - INFO - Received message from channel -1002093384030: [CHART]  is up **58%** [CHART]
from  [Entry Signal](https://t.me/solearlytrending/264018)

**$69.4K*...
2025-06-21 18:01:17,509 - signal_handler - INFO - Setting channel_identifier to 'Solana Early Trending' based on chat_id -1002093384030
2025-06-21 18:01:17,509 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 18:01:17,510 - signal_handler - INFO - No token addresses found in message
2025-06-21 18:01:17,511 - signal_handler - WARNING - Could not extract token address from message: [TRENDING_UP]  is up **58%** [TRENDING_UP]
from [?][?] [Entry Signal](https://t.me/solearlytrending/264018)

**$69.4K** [?]> **$11... (processing time: 0.000s)
2025-06-21 18:01:17,511 - signal_handler - DEBUG - No token address found in message from Solana Early Trending: [TRENDING_UP]  is up **58%** [TRENDING_UP]
from [?][?] [Entry Signal](https://t.me/solearlytrending/264018)

**$69.4K** [?]> **$11...
2025-06-21 18:01:23,369 - signal_handler - INFO - Received message from channel -1002187518673:  **Achievement Unlocked**: x2! 

@DONALD_CALL made a **x2+** call on [The Superior Ti...](https://t....
2025-06-21 18:01:23,369 - signal_handler - INFO - Setting channel_identifier to 'DONALD CALLS' based on chat_id -1002187518673
2025-06-21 18:01:23,369 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 18:01:23,369 - signal_handler - INFO - Found contract address with 'pump' suffix: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 18:01:23,370 - signal_handler - INFO - Using 'pump' address as highest priority: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 18:01:23,370 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 18:01:23,370 - signal_handler - INFO - Detected signal from DONALD CALLS for token: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 18:01:23,370 - signal_handler - INFO - Extracted signal: Token=5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump, Source=DONALD CALLS, Metrics Count=1
2025-06-21 18:01:23,370 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-21 18:01:23,370 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-21 18:01:23,371 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-21 18:01:23,371 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-21 18:01:23,371 - signal_handler - INFO - Added signal to queue: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump from DONALD CALLS with confidence 0.5, GMGN channel: False
2025-06-21 18:01:23,371 - signal_handler - INFO - Calling direct signal callback for 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 18:01:23,373 - signal_handler - INFO - Direct signal processing completed for 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 18:01:23,374 - signal_handler - INFO - Signal forwarded to bot controller: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump from DONALD CALLS (confidence: 0.50)
2025-06-21 18:01:23,374 - signal_handler - INFO - [msg_1750509083374_9966] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 18:01:23

 Token CA: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjp...
2025-06-21 18:01:23,374 - signal_handler - INFO - [msg_1750509083374_9966] Message queued with normal priority
2025-06-21 18:01:24,174 - signal_handler - INFO - Retrieved signal from queue: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump from DONALD CALLS
2025-06-21 18:01:24,184 - signal_handler - INFO - [msg_1750509083374_9966] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:01:26,852 - signal_handler - INFO - [msg_1750509083374_9966] Successfully resolved info channel entity: Bot Info
2025-06-21 18:01:26,999 - signal_handler - INFO - [msg_1750509083374_9966] Message sent directly successfully using entity
2025-06-21 18:01:27,000 - signal_handler - INFO - [msg_1750509083374_9966] Message sent successfully via direct send method
2025-06-21 18:01:27,000 - signal_handler - INFO - [msg_1750509083374_9966] Message sent successfully on attempt 1
2025-06-21 18:01:29,297 - signal_handler - INFO - [msg_1750509089297_4159] [SELL NOTIFICATION]: [GREEN] [BUY EXECUTED] [?] 2025-06-21 18:01:29

 Signal Source: DONALD CALLS
 Token: 5LN9HNUwbHjSvft...
2025-06-21 18:01:29,298 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750509089297_4159.txt
2025-06-21 18:01:29,298 - signal_handler - INFO - [msg_1750509089297_4159] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:01:29,381 - signal_handler - INFO - [msg_1750509089297_4159] Successfully resolved info channel entity: Bot Info
2025-06-21 18:01:29,509 - signal_handler - INFO - [msg_1750509089297_4159] Message sent directly successfully using entity
2025-06-21 18:01:29,510 - signal_handler - INFO - [msg_1750509089297_4159] Message sent directly successfully
2025-06-21 18:01:29,510 - signal_handler - INFO - [msg_1750509089510_1167] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (REAL) - 5LN9HNUW

 Timestamp: 2025-06-21 18:01:29
[REPEAT] Trade: R...
2025-06-21 18:01:29,510 - signal_handler - INFO - [msg_1750509089510_1167] Message queued with normal priority
2025-06-21 18:01:29,524 - signal_handler - INFO - [msg_1750509089510_1167] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:01:29,610 - signal_handler - INFO - [msg_1750509089510_1167] Successfully resolved info channel entity: Bot Info
2025-06-21 18:01:29,724 - signal_handler - INFO - [msg_1750509089510_1167] Message sent directly successfully using entity
2025-06-21 18:01:29,725 - signal_handler - INFO - [msg_1750509089510_1167] Message sent successfully via direct send method
2025-06-21 18:01:29,725 - signal_handler - INFO - [msg_1750509089510_1167] Message sent successfully on attempt 1
2025-06-21 18:01:43,694 - signal_handler - INFO - Received message from channel -1001509251052: [REFRESH] Update: **$$**

[ROCKET] x2.1
[CHART] MarketCap 60.74k  129.68k...
2025-06-21 18:01:43,694 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 18:01:43,694 - signal_handler - INFO - No token addresses found in message
2025-06-21 18:01:43,695 - signal_handler - WARNING - Could not extract token address from message: [REFRESH] Update: **$$**

[ROCKET] x2.1
[TRENDING_UP] MarketCap 60.74k [?] 129.68k... (processing time: 0.000s)
2025-06-21 18:01:43,695 - signal_handler - DEBUG - No token address found in message from None: [REFRESH] Update: **$$**

[ROCKET] x2.1
[TRENDING_UP] MarketCap 60.74k [?] 129.68k...
2025-06-21 18:01:57,122 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$8743.4248(+213.3%)...
2025-06-21 18:01:57,123 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 18:01:57,123 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 18:01:57,123 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 18:01:57,124 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 18:01:57,124 - signal_handler - INFO - Found contract address with 'pump' suffix: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump
2025-06-21 18:01:57,124 - signal_handler - INFO - Found contract address: A67Y8WNmzs3erqFZdAEBrMgrPqvhVG6teBeb2FZDYzTN
2025-06-21 18:01:57,124 - signal_handler - INFO - Using 'pump' address as highest priority: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump
2025-06-21 18:01:57,125 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump
2025-06-21 18:01:57,125 - signal_handler - INFO - Detected GMGN format
2025-06-21 18:01:57,125 - signal_handler - INFO - Found token symbol: 8743
2025-06-21 18:01:57,126 - signal_handler - INFO - Found FDV: 8743.4248 - 8.74K (+213.3%)
2025-06-21 18:01:57,126 - signal_handler - INFO - Detected GMGN channel signal for token: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump
2025-06-21 18:01:57,126 - signal_handler - INFO - Extracted signal: Token=Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 18:01:57,126 - signal_handler - INFO - Added signal to queue: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 18:01:57,126 - signal_handler - INFO - Calling direct signal callback for Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump
2025-06-21 18:01:57,130 - signal_handler - INFO - Direct signal processing completed for Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump
2025-06-21 18:01:57,130 - signal_handler - INFO - Signal forwarded to bot controller: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 18:01:57,131 - signal_handler - INFO - [msg_1750509117130_9328] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 18:01:57

 Token CA: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopu...
2025-06-21 18:01:57,131 - signal_handler - INFO - [msg_1750509117130_9328] Message queued with normal priority
2025-06-21 18:01:59,362 - signal_handler - INFO - [msg_1750509119361_5468] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-21 18:01:59

 Token: Cu9jQg7VR9YynwwFpoazm31h6o...
2025-06-21 18:01:59,362 - signal_handler - INFO - [msg_1750509119361_5468] Message queued with normal priority
2025-06-21 18:01:59,369 - signal_handler - INFO - Retrieved signal from queue: Cu9jQg7VR9YynwwFpoazm31h6o9v4uKBDJPfeQopump from solana signal alert - gmgn
2025-06-21 18:01:59,371 - signal_handler - INFO - [msg_1750509117130_9328] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:02:00,831 - signal_handler - INFO - Received sell notification: [PURPLE] [SELL [?] TP1 HIT (86.3% >= 13.0%)] [?] 2025-06-21 18:02:00
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Triggere...
2025-06-21 18:02:00,831 - signal_handler - INFO - [msg_1750509120831_2836] [SELL NOTIFICATION]:  [SELL [?] TP1 HIT (86.3% >= 13.0%)] [?] 2025-06-21 18:02:00

 Triggered by: DONALD CALLS
 Token: 5L...
2025-06-21 18:02:00,832 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750509120831_2836.txt
2025-06-21 18:02:00,833 - signal_handler - INFO - [msg_1750509120831_2836] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:02:00,849 - signal_handler - INFO - [msg_1750509117130_9328] Successfully resolved info channel entity: Bot Info
2025-06-21 18:02:00,916 - signal_handler - INFO - [msg_1750509120831_2836] Successfully resolved info channel entity: Bot Info
2025-06-21 18:02:00,967 - signal_handler - INFO - [msg_1750509117130_9328] Message sent directly successfully using entity
2025-06-21 18:02:00,967 - signal_handler - INFO - [msg_1750509117130_9328] Message sent successfully via direct send method
2025-06-21 18:02:00,968 - signal_handler - INFO - [msg_1750509117130_9328] Message sent successfully on attempt 1
2025-06-21 18:02:00,968 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 18:02:01,064 - signal_handler - INFO - [msg_1750509120831_2836] Message sent directly successfully using entity
2025-06-21 18:02:01,064 - signal_handler - INFO - [msg_1750509120831_2836] Message sent directly successfully
2025-06-21 18:02:01,065 - signal_handler - INFO - [msg_1750509121065_9281] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-21 18:02:01
[REPEAT] Trade: REAL

[MONEY] Star...
2025-06-21 18:02:01,065 - signal_handler - INFO - [msg_1750509121065_9281] Message queued with normal priority
2025-06-21 18:02:01,476 - signal_handler - INFO - [msg_1750509119361_5468] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:02:01,558 - signal_handler - INFO - [msg_1750509119361_5468] Successfully resolved info channel entity: Bot Info
2025-06-21 18:02:01,680 - signal_handler - INFO - [msg_1750509119361_5468] Message sent directly successfully using entity
2025-06-21 18:02:01,681 - signal_handler - INFO - [msg_1750509119361_5468] Message sent successfully via direct send method
2025-06-21 18:02:01,681 - signal_handler - INFO - [msg_1750509119361_5468] Message sent successfully on attempt 1
2025-06-21 18:02:01,681 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-21 18:02:01,681 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 18:02:02,196 - signal_handler - INFO - [msg_1750509121065_9281] Resolving info channel entity for ID: -1002362136450
2025-06-21 18:02:02,282 - signal_handler - INFO - [msg_1750509121065_9281] Successfully resolved info channel entity: Bot Info
2025-06-21 18:02:02,407 - signal_handler - INFO - [msg_1750509121065_9281] Message sent directly successfully using entity
2025-06-21 18:02:02,408 - signal_handler - INFO - [msg_1750509121065_9281] Message sent successfully via direct send method
2025-06-21 18:02:02,408 - signal_handler - INFO - [msg_1750509121065_9281] Message sent successfully on attempt 1
