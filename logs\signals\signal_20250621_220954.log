2025-06-21 22:09:54,894 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 22:09:54,895 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 22:09:54,900 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 22:09:54,901 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750523994
2025-06-21 22:09:54,901 - signal_handler - INFO - Using session path: c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750523994
2025-06-21 22:09:54,901 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 22:09:54,908 - signal_handler - INFO - Direct signal callback registered
2025-06-21 22:09:54,910 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 22:09:54,910 - signal_handler - INFO - API ID loaded: True
2025-06-21 22:09:54,910 - signal_handler - INFO - API Hash loaded: True
2025-06-21 22:09:54,910 - signal_handler - INFO - Phone number loaded: True
2025-06-21 22:09:54,910 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 22:09:54,910 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 22:09:54,910 - signal_handler - INFO - Using saved session string
2025-06-21 22:09:54,911 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 22:09:56,053 - signal_handler - INFO - Checking authorization status...
2025-06-21 22:09:56,241 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 22:09:56,241 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 22:09:56,242 - signal_handler - INFO - Checking authorization status...
2025-06-21 22:09:56,432 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 22:09:56,432 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 22:09:56,433 - signal_handler - INFO - Checking authorization status...
2025-06-21 22:09:56,620 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 22:09:56,620 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 22:09:56,621 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 22:09:56,621 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 22:09:57,039 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 22:09:57,040 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 22:09:57,040 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 22:09:57,040 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 22:09:57,041 - signal_handler - INFO - Direct signal callback registered
2025-06-21 22:09:57,491 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 22:10:00,224 - signal_handler - INFO - [msg_1750524000221_8141] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 22:10:00

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 22:10:00,224 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 22:10:00,224 - signal_handler - INFO - [msg_1750524000221_8141] Message queued with normal priority
2025-06-21 22:10:00,244 - signal_handler - INFO - [msg_1750524000244_2942] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 22:10:00
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 22:10:00,245 - signal_handler - INFO - [msg_1750524000244_2942] Message queued with normal priority
2025-06-21 22:10:00,427 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 22:10:00,427 - signal_handler - INFO - [msg_1750524000221_8141] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:10:00,616 - signal_handler - INFO - [msg_1750524000221_8141] Successfully resolved info channel entity: Bot Info
2025-06-21 22:10:00,837 - signal_handler - INFO - [msg_1750524000221_8141] Message sent directly successfully using entity
2025-06-21 22:10:00,837 - signal_handler - INFO - [msg_1750524000221_8141] Message sent successfully via direct send method
2025-06-21 22:10:00,837 - signal_handler - INFO - [msg_1750524000221_8141] Message sent successfully on attempt 1
2025-06-21 22:10:00,838 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 22:10:01,349 - signal_handler - INFO - [msg_1750524000244_2942] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:10:01,536 - signal_handler - INFO - [msg_1750524000244_2942] Successfully resolved info channel entity: Bot Info
2025-06-21 22:10:01,764 - signal_handler - INFO - [msg_1750524000244_2942] Message sent directly successfully using entity
2025-06-21 22:10:01,765 - signal_handler - INFO - [msg_1750524000244_2942] Message sent successfully via direct send method
2025-06-21 22:10:01,765 - signal_handler - INFO - [msg_1750524000244_2942] Message sent successfully on attempt 1
2025-06-21 22:10:16,674 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 22:10:16,675 - signal_handler - INFO - API ID loaded: True
2025-06-21 22:10:16,675 - signal_handler - INFO - API Hash loaded: True
2025-06-21 22:10:16,675 - signal_handler - INFO - Phone number loaded: True
2025-06-21 22:10:16,675 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 22:10:16,675 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 22:10:16,675 - signal_handler - INFO - Checking authorization status...
2025-06-21 22:10:16,865 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 22:10:16,865 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 22:10:16,866 - signal_handler - INFO - Checking authorization status...
2025-06-21 22:10:17,056 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 22:10:17,057 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 22:10:17,057 - signal_handler - INFO - Checking authorization status...
2025-06-21 22:10:17,244 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 22:10:17,244 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 22:10:17,245 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 22:10:17,245 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 22:10:17,473 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 22:10:17,476 - signal_handler - INFO - [msg_1750524017476_1935] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 22:10:17
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 22:10:17,476 - signal_handler - INFO - [msg_1750524017476_1935] Message queued with normal priority
2025-06-21 22:10:17,562 - signal_handler - INFO - [msg_1750524017476_1935] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:10:17,750 - signal_handler - INFO - [msg_1750524017476_1935] Successfully resolved info channel entity: Bot Info
2025-06-21 22:10:17,975 - signal_handler - INFO - [msg_1750524017476_1935] Message sent directly successfully using entity
2025-06-21 22:10:17,976 - signal_handler - INFO - [msg_1750524017476_1935] Message sent successfully via direct send method
2025-06-21 22:10:17,976 - signal_handler - INFO - [msg_1750524017476_1935] Message sent successfully on attempt 1
2025-06-21 22:10:17,977 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 22:10:17,977 - signal_handler - INFO - Direct signal callback registered
2025-06-21 22:10:17,978 - signal_handler - INFO - [msg_1750524017978_8563] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capital: 1.5...
2025-06-21 22:10:17,978 - signal_handler - INFO - [msg_1750524017978_8563] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:10:18,167 - signal_handler - INFO - [msg_1750524017978_8563] Successfully resolved info channel entity: Bot Info
2025-06-21 22:10:18,412 - signal_handler - INFO - [msg_1750524017978_8563] Message sent directly successfully using entity
2025-06-21 22:10:18,412 - signal_handler - INFO - [msg_1750524017978_8563] Message sent directly successfully
2025-06-21 22:10:29,237 - signal_handler - INFO - Received message from channel -1001509251052: [ALARM] NEW POTENTIAL GEM!

**First Solana Fork** [?] **$VELAS**
`HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5Zw...
2025-06-21 22:10:29,237 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 22:10:29,238 - signal_handler - INFO - Found contract address with 'pump' suffix: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:10:29,239 - signal_handler - INFO - Found contract address: AJUqULg2sfr6B1YeTjXzDHVPwLAkvrkJrk8NZpp3Mo8p
2025-06-21 22:10:29,239 - signal_handler - INFO - Found contract address: 624RwQjh6E8Vywr35QzA6AVew3rEPzGxhy8m54ChtoSU
2025-06-21 22:10:29,239 - signal_handler - INFO - Found contract address: 5B52w1ZW9tuwUduueP5J7HXz5AcGfruGoX6YoAudvyxG
2025-06-21 22:10:29,239 - signal_handler - INFO - Found contract address: EzUZNzbCifnX6moc5NmEJHGfDxYYSMyHfFTMrN3FfbCE
2025-06-21 22:10:29,240 - signal_handler - INFO - Found contract address: HTCczXYXkDpEcnJqpSPd2VqvgzAfd32T8RFgLSLmKQcy
2025-06-21 22:10:29,240 - signal_handler - INFO - Using 'pump' address as highest priority: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:10:29,240 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:10:29,240 - signal_handler - INFO - Detected Pepe Calls format
2025-06-21 22:10:29,241 - signal_handler - INFO - Found token symbol: VELAS
2025-06-21 22:10:29,241 - signal_handler - INFO - Extracted signal: Token=HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump, Source=None, Metrics Count=2
2025-06-21 22:10:29,241 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-21 22:10:29,242 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-21 22:10:29,242 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-21 22:10:29,242 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-21 22:10:29,242 - signal_handler - INFO - Added signal to queue: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from None with confidence 0.5, GMGN channel: False
2025-06-21 22:10:29,242 - signal_handler - INFO - Calling direct signal callback for HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:10:29,245 - signal_handler - INFO - Direct signal processing completed for HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:10:29,245 - signal_handler - INFO - Signal forwarded to bot controller: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from None (confidence: 0.50)
2025-06-21 22:10:29,245 - signal_handler - INFO - Retrieved signal from queue: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from None
2025-06-21 22:10:35,587 - signal_handler - INFO - [msg_1750524035586_8784] [SELL NOTIFICATION]: [GREEN] [BUY EXECUTED] [?] 2025-06-21 22:10:35

 Signal Source: None
 Token: HVpBgvP1ua68rHzk8s8tfUc...
2025-06-21 22:10:35,588 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750524035586_8784.txt
2025-06-21 22:10:35,588 - signal_handler - INFO - [msg_1750524035586_8784] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:10:35,776 - signal_handler - INFO - [msg_1750524035586_8784] Successfully resolved info channel entity: Bot Info
2025-06-21 22:10:36,015 - signal_handler - INFO - [msg_1750524035586_8784] Message sent directly successfully using entity
2025-06-21 22:10:36,016 - signal_handler - INFO - [msg_1750524035586_8784] Message sent directly successfully
2025-06-21 22:10:36,016 - signal_handler - INFO - [msg_1750524036016_9196] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (REAL) - HVPBGVP1

 Timestamp: 2025-06-21 22:10:36
[REPEAT] Trade: R...
2025-06-21 22:10:36,016 - signal_handler - INFO - [msg_1750524036016_9196] Message queued with normal priority
2025-06-21 22:10:36,031 - signal_handler - INFO - [msg_1750524036016_9196] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:10:36,220 - signal_handler - INFO - [msg_1750524036016_9196] Successfully resolved info channel entity: Bot Info
2025-06-21 22:10:36,444 - signal_handler - INFO - [msg_1750524036016_9196] Message sent directly successfully using entity
2025-06-21 22:10:36,444 - signal_handler - INFO - [msg_1750524036016_9196] Message sent successfully via direct send method
2025-06-21 22:10:36,445 - signal_handler - INFO - [msg_1750524036016_9196] Message sent successfully on attempt 1
2025-06-21 22:11:05,672 - signal_handler - INFO - Received message from channel -1002093384030: [FIRE] [**First Solana Fork**](https://t.me/soul_sniper_bot?start=15_HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw...
2025-06-21 22:11:05,673 - signal_handler - INFO - Setting channel_identifier to 'Solana Early Trending' based on chat_id -1002093384030
2025-06-21 22:11:05,673 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 22:11:05,673 - signal_handler - INFO - Found contract address with 'pump' suffix in Soul Sniper link: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:11:05,673 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:11:05,674 - signal_handler - INFO - Detected signal from Solana Early Trending for token: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:11:05,674 - signal_handler - INFO - Extracted signal: Token=HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump, Source=Solana Early Trending, Metrics Count=1
2025-06-21 22:11:05,675 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:11:05,675 - signal_handler - INFO - Token HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump cooldown expired. Processing signal.
2025-06-21 22:11:05,675 - signal_handler - INFO - Added signal to queue: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from Solana Early Trending with confidence 0.5, GMGN channel: False
2025-06-21 22:11:05,675 - signal_handler - INFO - Calling direct signal callback for HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:11:05,679 - signal_handler - INFO - Direct signal processing completed for HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:11:05,679 - signal_handler - INFO - Signal forwarded to bot controller: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from Solana Early Trending (confidence: 0.50)
2025-06-21 22:11:05,680 - signal_handler - INFO - [msg_1750524065680_5218] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 22:11:05

 Token CA: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2p...
2025-06-21 22:11:05,680 - signal_handler - INFO - [msg_1750524065680_5218] Message queued with normal priority
2025-06-21 22:11:05,680 - signal_handler - INFO - Retrieved signal from queue: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from Solana Early Trending
2025-06-21 22:11:05,751 - signal_handler - INFO - [msg_1750524065680_5218] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:11:05,938 - signal_handler - INFO - [msg_1750524065680_5218] Successfully resolved info channel entity: Bot Info
2025-06-21 22:11:06,170 - signal_handler - INFO - [msg_1750524065680_5218] Message sent directly successfully using entity
2025-06-21 22:11:06,171 - signal_handler - INFO - [msg_1750524065680_5218] Message sent successfully via direct send method
2025-06-21 22:11:06,171 - signal_handler - INFO - [msg_1750524065680_5218] Message sent successfully on attempt 1
2025-06-21 22:12:19,085 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9465.7207(+226.2%)...
2025-06-21 22:12:19,086 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 22:12:19,086 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 22:12:19,086 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 22:12:19,087 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 22:12:19,087 - signal_handler - INFO - Found contract address with 'pump' suffix: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump
2025-06-21 22:12:19,088 - signal_handler - INFO - Found contract address: Ez2jp3rwXUbaTx7XwiHGaWVgTPFdzJoSg8TopqbxfaJN
2025-06-21 22:12:19,088 - signal_handler - INFO - Using 'pump' address as highest priority: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump
2025-06-21 22:12:19,088 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump
2025-06-21 22:12:19,089 - signal_handler - INFO - Detected GMGN format
2025-06-21 22:12:19,089 - signal_handler - INFO - Found token symbol: 9465
2025-06-21 22:12:19,090 - signal_handler - INFO - Found FDV: 9465.7207 - 9.47K (+226.2%)
2025-06-21 22:12:19,090 - signal_handler - INFO - Detected GMGN channel signal for token: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump
2025-06-21 22:12:19,091 - signal_handler - INFO - Extracted signal: Token=GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 22:12:19,091 - signal_handler - INFO - Added signal to queue: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 22:12:19,091 - signal_handler - INFO - Calling direct signal callback for GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump
2025-06-21 22:12:19,096 - signal_handler - INFO - Direct signal processing completed for GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump
2025-06-21 22:12:19,096 - signal_handler - INFO - Signal forwarded to bot controller: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 22:12:19,096 - signal_handler - INFO - Retrieved signal from queue: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWpump from solana signal alert - gmgn
2025-06-21 22:12:19,099 - signal_handler - INFO - [msg_1750524139099_2847] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 22:12:19

 Token CA: GRCnK6g9ABF5GgPvFzcXCWZAe4ByUHuw3zwRWezWp...
2025-06-21 22:12:19,099 - signal_handler - INFO - [msg_1750524139099_2847] Message queued with normal priority
2025-06-21 22:12:21,409 - signal_handler - INFO - [msg_1750524141409_2883] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-21 22:12:21

 Token: GRCnK6g9ABF5GgPvFzcXCWZAe4...
2025-06-21 22:12:21,410 - signal_handler - INFO - [msg_1750524141409_2883] Message queued with normal priority
2025-06-21 22:12:21,411 - signal_handler - INFO - [msg_1750524139099_2847] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:12:21,652 - signal_handler - INFO - [msg_1750524139099_2847] Successfully resolved info channel entity: Bot Info
2025-06-21 22:12:21,930 - signal_handler - INFO - [msg_1750524139099_2847] Message sent directly successfully using entity
2025-06-21 22:12:21,931 - signal_handler - INFO - [msg_1750524139099_2847] Message sent successfully via direct send method
2025-06-21 22:12:21,931 - signal_handler - INFO - [msg_1750524139099_2847] Message sent successfully on attempt 1
2025-06-21 22:12:21,931 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 22:12:22,444 - signal_handler - INFO - [msg_1750524141409_2883] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:12:22,634 - signal_handler - INFO - [msg_1750524141409_2883] Successfully resolved info channel entity: Bot Info
2025-06-21 22:12:22,857 - signal_handler - INFO - [msg_1750524141409_2883] Message sent directly successfully using entity
2025-06-21 22:12:22,857 - signal_handler - INFO - [msg_1750524141409_2883] Message sent successfully via direct send method
2025-06-21 22:12:22,857 - signal_handler - INFO - [msg_1750524141409_2883] Message sent successfully on attempt 1
2025-06-21 22:12:24,678 - signal_handler - INFO - Received message from channel -1002093384030: [CHART] [**GORBHOUSE**](https://www.geckoterminal.com/solana/tokens/GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6...
2025-06-21 22:12:24,679 - signal_handler - INFO - Setting channel_identifier to 'Solana Early Trending' based on chat_id -1002093384030
2025-06-21 22:12:24,679 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 22:12:24,679 - signal_handler - INFO - Found contract address with 'pump' suffix: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump
2025-06-21 22:12:24,679 - signal_handler - INFO - Using 'pump' address as highest priority: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump
2025-06-21 22:12:24,679 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump
2025-06-21 22:12:24,680 - signal_handler - INFO - Detected signal from Solana Early Trending for token: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump
2025-06-21 22:12:24,680 - signal_handler - INFO - Extracted signal: Token=GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump, Source=Solana Early Trending, Metrics Count=1
2025-06-21 22:12:24,680 - signal_handler - INFO - Added signal to queue: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump from Solana Early Trending with confidence 0.5, GMGN channel: False
2025-06-21 22:12:24,680 - signal_handler - INFO - Calling direct signal callback for GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump
2025-06-21 22:12:24,682 - signal_handler - INFO - Direct signal processing completed for GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump
2025-06-21 22:12:24,683 - signal_handler - INFO - Signal forwarded to bot controller: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump from Solana Early Trending (confidence: 0.50)
2025-06-21 22:12:24,683 - signal_handler - INFO - [msg_1750524144683_8100] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 22:12:24

 Token CA: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7p...
2025-06-21 22:12:24,683 - signal_handler - INFO - [msg_1750524144683_8100] Message queued with normal priority
2025-06-21 22:12:25,831 - signal_handler - INFO - [msg_1750524145831_3806] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] TOKEN NOT FOUND] [?] 2025-06-21 22:12:25

 Token: GTYRKAD5hD2DKGa27kfTrZz3...
2025-06-21 22:12:25,831 - signal_handler - INFO - [msg_1750524145831_3806] Message queued with normal priority
2025-06-21 22:12:25,832 - signal_handler - INFO - Retrieved signal from queue: GTYRKAD5hD2DKGa27kfTrZz3XfadKgw6bm9nZWh7pump from Solana Early Trending
2025-06-21 22:12:25,835 - signal_handler - INFO - [msg_1750524144683_8100] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:12:26,069 - signal_handler - INFO - [msg_1750524144683_8100] Successfully resolved info channel entity: Bot Info
2025-06-21 22:12:26,293 - signal_handler - INFO - [msg_1750524144683_8100] Message sent directly successfully using entity
2025-06-21 22:12:26,294 - signal_handler - INFO - [msg_1750524144683_8100] Message sent successfully via direct send method
2025-06-21 22:12:26,294 - signal_handler - INFO - [msg_1750524144683_8100] Message sent successfully on attempt 1
2025-06-21 22:12:26,295 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-21 22:12:26,295 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 22:12:26,810 - signal_handler - INFO - [msg_1750524145831_3806] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:12:27,000 - signal_handler - INFO - [msg_1750524145831_3806] Successfully resolved info channel entity: Bot Info
2025-06-21 22:12:27,228 - signal_handler - INFO - [msg_1750524145831_3806] Message sent directly successfully using entity
2025-06-21 22:12:27,228 - signal_handler - INFO - [msg_1750524145831_3806] Message sent successfully via direct send method
2025-06-21 22:12:27,228 - signal_handler - INFO - [msg_1750524145831_3806] Message sent successfully on attempt 1
2025-06-21 22:12:28,315 - signal_handler - INFO - Received message from channel -1002017173747: [ROCKET] NEW SOL HIGH VOLUME TOKEN [ROCKET]

            [TARGET] Token Symbol: $VELAS
             ...
2025-06-21 22:12:28,315 - signal_handler - INFO - Setting channel_identifier to 'SOL HIGH VOLUME ALERT | Bordga' based on chat_id -1002017173747
2025-06-21 22:12:28,316 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 22:12:28,316 - signal_handler - INFO - Found contract address with 'pump' suffix: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,316 - signal_handler - INFO - Found contract address: ajuqulg2sfr6b1yetjxzdhvpwlakvrkjrk8nzpp3mo8p
2025-06-21 22:12:28,317 - signal_handler - INFO - Using 'pump' address as highest priority: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,317 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,317 - signal_handler - INFO - Detected High Volume Alert format
2025-06-21 22:12:28,317 - signal_handler - INFO - Found token symbol: VELAS
2025-06-21 22:12:28,317 - signal_handler - INFO - Found market cap: 58K - 58.00K
2025-06-21 22:12:28,318 - signal_handler - INFO - Found age: 5 minutes
2025-06-21 22:12:28,318 - signal_handler - INFO - Found top holders: 21.71%
2025-06-21 22:12:28,318 - signal_handler - INFO - Found risk rating: Good
2025-06-21 22:12:28,318 - signal_handler - INFO - Detected signal from SOL HIGH VOLUME ALERT | Bordga for token: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,319 - signal_handler - INFO - Extracted signal: Token=HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump, Source=SOL HIGH VOLUME ALERT | Bordga, Metrics Count=7
2025-06-21 22:12:28,319 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,319 - signal_handler - INFO - Token HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump cooldown expired. Processing signal.
2025-06-21 22:12:28,319 - signal_handler - INFO - Added signal to queue: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from SOL HIGH VOLUME ALERT | Bordga with confidence 0.5, GMGN channel: False
2025-06-21 22:12:28,319 - signal_handler - INFO - Calling direct signal callback for HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,323 - signal_handler - INFO - Direct signal processing completed for HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump
2025-06-21 22:12:28,324 - signal_handler - INFO - Signal forwarded to bot controller: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from SOL HIGH VOLUME ALERT | Bordga (confidence: 0.50)
2025-06-21 22:12:28,324 - signal_handler - INFO - Retrieved signal from queue: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2pump from SOL HIGH VOLUME ALERT | Bordga
2025-06-21 22:12:28,327 - signal_handler - INFO - [msg_1750524148326_9429] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 22:12:28

 Token CA: HVpBgvP1ua68rHzk8s8tfUcY1NAvcKw5ZwR3n8u2p...
2025-06-21 22:12:28,327 - signal_handler - INFO - [msg_1750524148326_9429] Message queued with normal priority
2025-06-21 22:12:28,327 - signal_handler - INFO - [msg_1750524148326_9429] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:12:28,542 - signal_handler - INFO - [msg_1750524148326_9429] Successfully resolved info channel entity: Bot Info
2025-06-21 22:12:28,765 - signal_handler - INFO - [msg_1750524148326_9429] Message sent directly successfully using entity
2025-06-21 22:12:28,766 - signal_handler - INFO - [msg_1750524148326_9429] Message sent successfully via direct send method
2025-06-21 22:12:28,766 - signal_handler - INFO - [msg_1750524148326_9429] Message sent successfully on attempt 1
2025-06-21 22:13:42,669 - signal_handler - INFO - Received sell notification: [PURPLE] [SELL [?] MAX HOLD TIME EXCEEDED (3.0M >= 3M)] [?] 2025-06-21 22:13:42
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-21 22:13:42,670 - signal_handler - INFO - [msg_1750524222669_7273] [SELL NOTIFICATION]:  [SELL [?] MAX HOLD TIME EXCEEDED (3.0M >= 3M)] [?] 2025-06-21 22:13:42

 Triggered by: None
 Token:...
2025-06-21 22:13:42,671 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750524222669_7273.txt
2025-06-21 22:13:42,672 - signal_handler - INFO - [msg_1750524222669_7273] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:13:42,863 - signal_handler - INFO - [msg_1750524222669_7273] Successfully resolved info channel entity: Bot Info
2025-06-21 22:13:43,119 - signal_handler - INFO - [msg_1750524222669_7273] Message sent directly successfully using entity
2025-06-21 22:13:43,120 - signal_handler - INFO - [msg_1750524222669_7273] Message sent directly successfully
2025-06-21 22:13:43,120 - signal_handler - INFO - [msg_1750524223120_5397] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-21 22:13:43
[REPEAT] Trade: REAL

[MONEY] Star...
2025-06-21 22:13:43,121 - signal_handler - INFO - [msg_1750524223120_5397] Message queued with normal priority
2025-06-21 22:13:43,209 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-21 22:13:43,210 - signal_handler - INFO - [msg_1750524223120_5397] Resolving info channel entity for ID: -1002362136450
2025-06-21 22:13:43,480 - signal_handler - INFO - [msg_1750524223120_5397] Successfully resolved info channel entity: Bot Info
2025-06-21 22:13:43,835 - signal_handler - INFO - [msg_1750524223120_5397] Message sent directly successfully using entity
2025-06-21 22:13:43,835 - signal_handler - INFO - [msg_1750524223120_5397] Message sent successfully via direct send method
2025-06-21 22:13:43,835 - signal_handler - INFO - [msg_1750524223120_5397] Message sent successfully on attempt 1
