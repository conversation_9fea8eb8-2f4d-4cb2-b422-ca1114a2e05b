import re
import asyncio
import os
import json
import time
import logging
import random
import sqlite3
import traceback
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime
from collections import deque
from config_manager import ConfigManager

# OPTIMIZED: Consolidated pattern management
class TokenPatterns:
    """Centralized token pattern management for efficient regex operations"""

    # Base token address pattern
    TOKEN_ADDRESS_BASE = r'[1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?'

    # URL patterns grouped by platform
    URL_PATTERNS = {
        'soul_sniper': r'https?://(?:www\.)?t\.me/soul_sniper_bot\?start=(?:\d+_)?({token})',
        'telegram_bot': r'https?://(?:www\.)?t\.me/\w+\?start=(?:15_)?({token})',
        'geckoterminal': r'https?://(?:www\.)?geckoterminal\.com/solana/pools/({token})',
        'dexscreener': r'https?://(?:www\.)?dexscreener\.com/solana/({token})',
        'raydium': r'https?://(?:www\.)?raydium\.io/swap/\?inputCurrency=({token})',
        'birdeye': r'https?://(?:www\.)?birdeye\.so/token/({token})'
    }

    # Contract address patterns
    CA_PATTERNS = [
        r'(?:CA|ca|address|Address|CONTRACT|Contract|token)(?:\s*(?::|=|\s)\s*)({token})',
        r'Token Address:?\s*({token})',
        r'CA:?\s*({token})'
    ]

    # Performance report patterns (to filter out)
    PERFORMANCE_PATTERNS = [
        r'is up \d+[%X]', r'Achievement Unlocked: x\d+', r'made a x\d+\+?',
        r'\d+X \+ Done From Call', r'Called \d+K -> \d+K', r'JUST MADE \d+X'
    ]

    # Channel identification
    CHANNEL_PATTERNS = {
        'early': r"early|trending"
    }

    # Token symbol patterns
    SYMBOL_PATTERNS = {
        'standard': r'\$([A-Za-z0-9X_-]+)\s*\(([^)]+)\)',
        'short': r"\$([A-Z0-9]{2,10})",
        'long': r"\$([A-Z0-9]{2,20})",
        'markdown': r'\[\*\*([A-Za-z0-9X_\- ]+)\*\*\]'
    }

    def __init__(self):
        # Compile all patterns with token placeholder replaced
        self.compiled_patterns = {}
        self._compile_patterns()

    def _compile_patterns(self):
        """Compile all patterns for efficient matching"""
        token_pattern = self.TOKEN_ADDRESS_BASE

        # Compile URL patterns
        for name, pattern in self.URL_PATTERNS.items():
            self.compiled_patterns[f'url_{name}'] = re.compile(
                pattern.format(token=token_pattern), re.IGNORECASE
            )

        # Compile CA patterns
        for i, pattern in enumerate(self.CA_PATTERNS):
            self.compiled_patterns[f'ca_{i}'] = re.compile(
                pattern.format(token=token_pattern), re.IGNORECASE
            )

        # Compile other patterns
        self.compiled_patterns['token_general'] = re.compile(f"(?<!\\w)({token_pattern})(?!\\w)(?!.*go back to early)")
        self.compiled_patterns['insider_holdings'] = re.compile(r'TOP\s+10:\s+(?:\*\*)?(\d+(?:\.\d+)?)(?:\*\*)?\%')

        # Compile performance patterns
        self.compiled_patterns['performance'] = re.compile('|'.join(self.PERFORMANCE_PATTERNS), re.IGNORECASE)

        # Compile channel patterns
        for name, pattern in self.CHANNEL_PATTERNS.items():
            self.compiled_patterns[f'channel_{name}'] = re.compile(pattern, re.IGNORECASE)

        # Compile symbol patterns
        for name, pattern in self.SYMBOL_PATTERNS.items():
            self.compiled_patterns[f'symbol_{name}'] = re.compile(pattern, re.IGNORECASE)

# OPTIMIZED: Global pattern instance
PATTERNS = TokenPatterns()

# OPTIMIZED: Backward compatibility - maintain old pattern names for existing code
SOUL_SNIPER_REGEX = PATTERNS.compiled_patterns['url_soul_sniper']
TELEGRAM_BOT_START_REGEX = PATTERNS.compiled_patterns['url_telegram_bot']
GECKOTERMINAL_REGEX = PATTERNS.compiled_patterns['url_geckoterminal']
DEXSCREENER_URL_REGEX = PATTERNS.compiled_patterns['url_dexscreener']
RAYDIUM_URL_REGEX = PATTERNS.compiled_patterns['url_raydium']
BIRDEYE_URL_REGEX = PATTERNS.compiled_patterns['url_birdeye']

# Token address patterns
SOLANA_TOKEN_REGEX = PATTERNS.compiled_patterns['token_general']
CONTRACT_ADDRESS_REGEX = PATTERNS.compiled_patterns['token_general']
CA_LINE_REGEX = PATTERNS.compiled_patterns['ca_2']  # CA: pattern
TOKEN_ADDRESS_FIELD_REGEX = PATTERNS.compiled_patterns['ca_1']  # Token Address: pattern

# Token symbol patterns
TOKEN_NAME_REGEX = PATTERNS.compiled_patterns['symbol_standard']
TOKEN_SYMBOL_REGEX = PATTERNS.compiled_patterns['symbol_short']
TOKEN_SYMBOL_LONG_REGEX = PATTERNS.compiled_patterns['symbol_long']
MARKDOWN_TOKEN_NAME_REGEX = PATTERNS.compiled_patterns['symbol_markdown']

# Legacy patterns for compatibility
TOKEN_ADDRESS_PATTERNS = [
    r'(?:CA|ca|address|Address|CONTRACT|Contract|token)(?:\s*(?::|=|\s)\s*)([A-Za-z0-9]{32,44})',
    r'([A-Za-z0-9]{32,44}pump)',
    r'([A-Za-z0-9]{32,44})',
]

# OPTIMIZED: Constants moved to class-level
MAX_INSIDER_HOLDINGS_PERCENT = 30.0
from utils import (
    log_signal,
    log_error,
    log_historical_signal,
    format_sol_amount,
    format_usd_amount,
    format_percentage,
    log_signal_data
)
from telethon import TelegramClient, events
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, rpcerrorlist
# Add StringSession import for more reliable session storage
from telethon.sessions import StringSession

# Configure logging to reduce Telethon verbosity
logging.getLogger('telethon').setLevel(logging.WARNING)
logging.getLogger('telethon.network.mtprotosender').setLevel(logging.ERROR)

logger = logging.getLogger(__name__)

# Additional regex patterns for Telegram referral links
TELEGRAM_REFERRAL_REGEX = re.compile(r"start=\d+_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)")

# Additional patterns for specific message formats
VOLUME_SPECIFIC_REGEX = re.compile(r"Vol.*?__\w+__:\s*\$?([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
MCP_SPECIFIC_REGEX = re.compile(r"MC:\s*\$?([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
LIQ_SPECIFIC_REGEX = re.compile(r"Liq:\s*\$?([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
VOLUME_5M_REGEX = re.compile(r"5m\s*TXs/Vol.*?(\d+)/\$?([\d.,]+)K", re.IGNORECASE)
HOLDER_SPECIFIC_REGEX = re.compile(r"Holder:\s*(\d+)", re.IGNORECASE)
OPEN_SPECIFIC_REGEX = re.compile(r"Open:\s*(\d+)s", re.IGNORECASE)

# Market Cap patterns
MC_DOLLARS_REGEX = re.compile(r"MC[: ]*\$?(\d{1,6}(?:,\d{3})?)", re.IGNORECASE)
MCP_REGEX = re.compile(r"(?:MCAP|MCP|MCap|MC|mc)[: ]+\$?([0-9,.]+(?:[KMB])?)", re.IGNORECASE)

# Chain identifier regex
CHAIN_REGEX = re.compile(r"#?SOL\b", re.IGNORECASE)
CHAIN_SOL_REGEX = re.compile(r"\bSol\b", re.IGNORECASE)

# Pump marker regex
PUMP_MARKER_REGEX = re.compile(r"\d+x", re.IGNORECASE)

# Volume patterns
VOLUME_REGEX = re.compile(r"24H\s*VOL[: ]+\$?\d{1,6}[KM]", re.IGNORECASE)
VOLUME_1H_REGEX = re.compile(r"Vol[: ]*1h[: ]*\$?\d{1,6}[KM]", re.IGNORECASE)

# Liquidity patterns
LIQUIDITY_REGEX = re.compile(r"Liq[: ]*\$?\d{1,6}[KM]", re.IGNORECASE)
LIQUIDITY_SOL_REGEX = re.compile(r"Liq(?:uidity)?:?\s*(\d{1,4}(?:\.\d{1,2})?) SOL\s*\(\$(\d{1,2}(?:\.\d{1,2})?[KMB]?)\)", re.IGNORECASE)
LIQUIDITY_USD_REGEX = re.compile(r"(?:Liq|Liquidity|lq)[: ]*\$?([0-9,.]+(?:[KMB])?)", re.IGNORECASE)

# Holders patterns
HOLDERS_REGEX = re.compile(r"Hodls[: ]*\d{1,5}", re.IGNORECASE)
HOLDER_COUNT_REGEX = re.compile(r"Holder[: ]*\d+", re.IGNORECASE)

# Age patterns
AGE_REGEX = re.compile(r"Age[: ]*\d{1,3}(m|min)", re.IGNORECASE)
OPEN_REGEX = re.compile(r"Open[: ]*\d{1,3}min", re.IGNORECASE)

# Status patterns
STATUS_REGEX = re.compile(r"Status:?\s*([\d.,]+)%", re.IGNORECASE)
LP_BURN_REGEX = re.compile(r"(✅|❌)\s*LP\s*Burn", re.IGNORECASE)
TOP_HOLDERS_REGEX = re.compile(r"(?:TOP\s*10|Top\s*10)[: ]+\d{1,3}(?:\.\d+)?%", re.IGNORECASE)
RISK_RATING_REGEX = re.compile(r"Risks?:\s*(Good|Bad)", re.IGNORECASE)

# Developer patterns
DEV_STATUS_REGEX = re.compile(r"Dev[: ]\d{1,4} SOL\s*|\s*0%\s*\$[A-Z0-9]{2,10}", re.IGNORECASE)
DEV_ACTION_REGEX = re.compile(r"DEV[: ]*Sell All", re.IGNORECASE)
DEV_ACTIONS_REGEX = re.compile(r"DEV:?\s*(Sell All|[0-9.]+\s*SOL\s*\|\s*0%.*?)", re.IGNORECASE)

# Sold percentage
SOLD_REGEX = re.compile(r"Sold[: ]\d{1,3}\.\d{1,2}%", re.IGNORECASE)

# FDV patterns
FDV_REGEX = re.compile(r"FDV.*?\$([0-9.]+[KM]?)\s*\(\+([0-9.]+)%\)", re.IGNORECASE)
FDV_PERCENT_REGEX = re.compile(r"FDV.*?\+([0-9.]+)%", re.IGNORECASE)

# Price patterns
PRICE_REGEX = re.compile(r"Price:?\s*\$([0-9.]+)", re.IGNORECASE)

# Transaction patterns
TXS_REGEX = re.compile(r"([0-9,]+)\s*(?:txs|TXs|transactions)", re.IGNORECASE)
TXS_VOL_REGEX = re.compile(r"(?:TXs|5m\s*TXs)\/Vol:?\s*(\d+)\/\$?([0-9,.]+[KM]?)", re.IGNORECASE)



# Social Links
DEX_URL_REGEX = re.compile(r"DexS:?\s*(https?://(?:www\.)?(?:dexscreener\.com|birdeye\.so|solscan\.io|raydium\.io|jup\.ag)\/(?:solana|token|swap)\/[^\s]+)", re.IGNORECASE)

# Define session file name centrally
TELEGRAM_SESSION_FILE = "trading_real_session"
# New session string storage file
SESSION_STRING_FILE = "session_string.json"

class SignalRateMonitor:
    """Monitor signal processing rates and detect issues"""
    def __init__(self):
        self.hourly_counts = {}
        self.expected_hourly = {
            "Solbix": 2,  # ~50/day = 2/hour
            "Solana Early Trending": 1  # ~30/day = 1/hour
        }
        self.last_health_check = 0

    def log_signal(self, source):
        """Log a signal from a specific source"""
        hour = int(time.time() // 3600)
        if hour not in self.hourly_counts:
            self.hourly_counts[hour] = {}
        if source not in self.hourly_counts[hour]:
            self.hourly_counts[hour][source] = 0
        self.hourly_counts[hour][source] += 1

    def check_signal_health(self):
        """Check if signal rates are healthy and log warnings"""
        current_time = time.time()

        # Only check once per hour
        if current_time - self.last_health_check < 3600:
            return

        hour = int(current_time // 3600)
        if hour not in self.hourly_counts:
            return

        for source, expected in self.expected_hourly.items():
            actual = self.hourly_counts[hour].get(source, 0)
            if actual < expected * 0.3:  # Less than 30% expected
                logger.warning(f"LOW SIGNAL VOLUME: {source} - {actual}/{expected} expected this hour")

        self.last_health_check = current_time

    def check_channel_health(self, signal_handler):
        """Check health of individual channels"""
        current_time = time.time()

        # Only check once per hour
        if current_time - self.last_health_check < 3600:
            return

        logger.info("=== CHANNEL HEALTH REPORT ===")
        for channel_id, health in signal_handler.channel_health.items():
            name = health["name"]
            total = health["total_signals"]
            last_signal = health["last_signal"]

            if last_signal == 0:
                status = "❌ NO SIGNALS EVER"
            else:
                hours_since = (current_time - last_signal) / 3600
                if hours_since > 24:
                    status = f"❌ SILENT {hours_since:.1f}h"
                elif hours_since > 6:
                    status = f"⚠️ QUIET {hours_since:.1f}h"
                else:
                    status = f"✅ ACTIVE {hours_since:.1f}h ago"

            logger.info(f"{name}: {total} signals, {status}")
        logger.info("=== END CHANNEL HEALTH ===")

class SignalHandler:
    def __init__(self, config: ConfigManager):
        self.config_manager = config
        self.telegram_settings = self.config_manager.get_telegram_settings()
        self.trading_settings = self.config_manager.get_trading_settings()

        # Signal processing control
        self.signal_processing_enabled = True  # Default to enabled, can be disabled via connect()

        # Get API ID and API Hash from environment variables
        self.api_id = self.config_manager.get_telegram_api_id()
        self.api_hash = self.config_manager.get_telegram_api_hash()
        # Add logging to verify API ID/Hash - safely handle None values
        if self.api_id and isinstance(self.api_id, str) and len(self.api_id) > 10:
            logger.debug(f"Loaded Telegram API ID: {self.api_id[:5]}...{self.api_id[-5:]}")
        else:
            logger.debug(f"Loaded Telegram API ID: {self.api_id}")

        if self.api_hash and isinstance(self.api_hash, str):
            logger.debug(f"Loaded Telegram API Hash: {'*' * len(self.api_hash)}")
        else:
            logger.debug(f"Loaded Telegram API Hash: None")

        if not self.api_id or not self.api_hash:
            logger.warning("Telegram API ID or API Hash is missing from configuration! Telegram functionality will be limited.")

        self.bot_token = self.telegram_settings.get('bot_token')
        self.target_channels = self.telegram_settings.get('target_channels', [])
        self.excluded_channels = self.telegram_settings.get('excluded_channels', [])

        self.confidence_threshold = self.trading_settings.get('confidence_threshold', 0.7)
        self.cooldown_period_seconds = self.trading_settings.get('cooldown_period_seconds', 300)

        self.client = None
        # SURGICAL FIX: Add queue size limit to prevent memory overflow (simple & safe)
        self.signal_queue = asyncio.Queue(maxsize=500)  # Reasonable limit for signal buffering
        self.is_listening = False

        # Channel settings - ONLY use channels explicitly configured in target_channels
        self.solearlytrending_channel_id = self.config_manager.get('telegram_settings', 'solearlytrending_channel_id')  # No default - only if explicitly set

        # Session file handling - FORCE FRESH SESSION to clear old channel subscriptions
        base_session_name = self.telegram_settings.get('bot_session_name', 'trading_real_session')
        timestamp = int(time.time())
        self.session_name = f"{base_session_name}_fresh_{timestamp}"  # Force fresh session
        self.session_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), self.session_name)
        self.string_session_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), SESSION_STRING_FILE)
        self.session_string = self._load_session_string()
        logger.info(f"Using unique session name with timestamp: {self.session_name}")
        logger.info(f"Using session path: {self.session_path}")

        # Regex patterns for different sources - comprehensive coverage for all channels
        self.regex_patterns = {
            'sol_address': r'\b[1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?\b',
            'soul_sniper': r'https?:\/\/(?:www\.)?t\.me\/soul_sniper_bot\?start=(?:\d+_)?([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)',

            'early': r"Solana Early Trending",
            'dexscreener': r'https?:\/\/(?:www\.)?dexscreener\.com\/solana\/([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)(?:\?|$)',
            'geckoterminal': r'https?:\/\/(?:www\.)?geckoterminal\.com\/solana\/pools\/([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)(?:\?|$)',
            'raydium': r'https?:\/\/(?:www\.)?raydium\.io\/swap\/\?inputCurrency=([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)',
            'birdeye': r'https?:\/\/(?:www\.)?birdeye\.so\/token\/([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)',
            'telegram_bot': r'https?:\/\/(?:www\.)?t\.me\/\w+\?start=(?:15_)?([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
        }



        # Signal processing
        self.recent_signals = []  # Store recent token addresses
        self.recent_signal_data = {}  # Store detailed signal data
        self.processed_signals = set()
        self.signal_lock = asyncio.Lock()
        self.token_confidence = {}  # Store confidence scores for tokens

        # SURGICAL FIX: Queue-level deduplication tracking
        self.recent_queue_signals = {}  # token_address -> timestamp for queue deduplication

        # SURGICAL FIX: Signal rate monitoring
        self.signal_rate_monitor = SignalRateMonitor()

        # SURGICAL FIX: Channel health tracking
        self.channel_health = {
            -1001509251052: {"name": "Solana Gems Radar", "last_signal": 0, "total_signals": 0},
            -1002187518673: {"name": "DONALD CALLS", "last_signal": 0, "total_signals": 0},
            -1002017173747: {"name": "SOL HIGH VOLUME ALERT | Bordga", "last_signal": 0, "total_signals": 0},
            -1002017857449: {"name": "PEPE CALLS", "last_signal": 0, "total_signals": 0},
            -1002177594166: {"name": "OxyZen Calls", "last_signal": 0, "total_signals": 0},
            -1002093384030: {"name": "Solana Early Trending", "last_signal": 0, "total_signals": 0},
            -1002428819353: {"name": "Solbix Community Calls", "last_signal": 0, "total_signals": 0}
        }

        # SURGICAL FIX: Signal processing health tracking
        self.signals_received_count = 0
        self.signals_processed_count = 0
        self.signals_dropped_count = 0
        self.last_health_log_time = 0

        # Ensure signal queue is properly initialized
        if not hasattr(self, 'signal_queue') or self.signal_queue is None:
            logger.info("Initializing signal queue")
            self.signal_queue = asyncio.Queue()

        # Log queue status
        try:
            queue_size = self.signal_queue.qsize()
            logger.info(f"Signal queue initialized with size: {queue_size}")
        except Exception as e:
            logger.error(f"Error checking queue size: {e}")

        # Cooldown tracking with optimized timing
        self.cooldowns = {}  # token_address -> timestamp
        self.cooldown_period = 5  # Reduced from 15s to 5s for faster response to signals

        # Special cooldown for pump tokens (much shorter)
        self.pump_token_cooldown_period = 1  # Only 1 second cooldown for pump tokens

        # Channel tracking with optimized cooldown
        self.channel_cooldowns = {}  # channel_id -> timestamp
        self.channel_cooldown_period = 5  # Reduced to 5s for active channels like Solbix

        # Technical analysis
        self.price_history = {}  # token_address -> List[float]
        self.volume_history = {}  # token_address -> List[float]
        self.max_history = 100  # Keep last 100 price points

        # Telegram status
        self.telegram_status = "DISCONNECTED"

        # Simulation mode removed - real signals only
        # Set default run mode to REAL
        self.run_mode = "REAL"

        # Store additional information about signals
        self.recent_signals_info = {}  # token_address -> {'source_channel': name, 'timestamp': time}

        self.my_id = None # ADDED: Initialize bot's own ID storage

    def extract_ca_id_only(self, message_text: str, channel_info: dict) -> Optional[str]:
        """
        Extract ONLY the Contract Address ID from signal messages
        Enhanced for Solana Early Trending channel where CA is in hyperlinks
        Enhanced for Pumpfun Volume Alert channel where CA is after "Mint:"
        """

        # CRITICAL: For Solana Early Trending - ONLY accept soul_sniper_bot links with start=15_ pattern
        if channel_info.get('channel_id') == self.solearlytrending_channel_id:
            # ONLY accept Soul Sniper bot links with start=15_ pattern - NO OTHER FORMATS
            soul_sniper_pattern = r'https://t\.me/soul_sniper_bot\?start=15_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
            match = re.search(soul_sniper_pattern, message_text)
            if match:
                ca_id = match.group(1)
                logger.info(f"SOLANA EARLY TRENDING: Extracted CA from soul_sniper_bot link: {ca_id}")
                return ca_id
            else:
                logger.warning(f"SOLANA EARLY TRENDING: No soul_sniper_bot link with 15_ prefix found. Skipping message.")
                logger.debug(f"SOLANA EARLY TRENDING: Message content: {message_text[:200]}...")
                return None



        # Standard extraction for other channels
        # Priority 1: Pump.fun tokens (highest priority)
        pump_match = re.search(r'([1-9A-HJ-NP-Za-km-z]{32,44}pump)', message_text)
        if pump_match:
            logger.info(f"Found pump.fun token: {pump_match.group(1)}")
            return pump_match.group(1)

        # Priority 2: Direct CA patterns
        ca_patterns = [
            r'(?:CA|ca|Contract|TOKEN)\s*:?\s*([1-9A-HJ-NP-Za-km-z]{32,44})',
            r'Token Address:?\s*([1-9A-HJ-NP-Za-km-z]{32,44})',
        ]

        for pattern in ca_patterns:
            match = re.search(pattern, message_text, re.IGNORECASE)
            if match:
                logger.info(f"Found CA from pattern: {match.group(1)}")
                return match.group(1)

        # Priority 3: URL extraction (FIXED - removed GeckoTerminal pool extraction)
        url_patterns = [
            r'dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{32,44})',
            r't\.me/\w+\?start=(?:\d+_)?([1-9A-HJ-NP-Za-km-z]{32,44})',
            # REMOVED: r'geckoterminal\.com/solana/pools/([1-9A-HJ-NP-Za-km-z]{32,44})', - Contains pool addresses, not token addresses
        ]

        for pattern in url_patterns:
            match = re.search(pattern, message_text, re.IGNORECASE)
            if match:
                logger.info(f"Found CA from URL: {match.group(1)}")
                return match.group(1)

        # Priority 4: Any valid Solana address
        general_match = re.search(r'([1-9A-HJ-NP-Za-km-z]{32,44})', message_text)
        if general_match:
            logger.info(f"Found general CA: {general_match.group(1)}")
            return general_match.group(1)

        return None

    def _load_session_string(self) -> str:
        """Load the session string from file if it exists"""
        # Restore original functionality: load from session_string.json
        if os.path.exists(self.string_session_file):
            try:
                with open(self.string_session_file, 'r') as f:
                    data = json.load(f)
                    session_str = data.get('session_string')
                    if session_str and isinstance(session_str, str) and len(session_str) > 10:
                        logger.info(f"Loaded session string from {self.string_session_file} (length: {len(session_str)})")
                        return session_str
                    else:
                        logger.warning(f"Invalid or empty session string found in {self.string_session_file}")
                        # Optionally, delete the invalid file
                        # os.remove(self.string_session_file)
                        # logger.info(f"Removed invalid session string file: {self.string_session_file}")
                        return "" # Return empty if invalid
            except json.JSONDecodeError:
                logger.error(f"Error decoding JSON from session string file: {self.string_session_file}. File might be corrupted.")
                # Optionally, delete the corrupted file
                # os.remove(self.string_session_file)
                # logger.info(f"Removed corrupted session string file: {self.string_session_file}")
                return "" # Return empty if corrupted
            except Exception as e:
                logger.error(f"Error loading session string from {self.string_session_file}: {e}")
                return "" # Return empty on other errors
        else:
            logger.info(f"Session string file not found: {self.string_session_file}. Will create a new session.")
        return ""

    def _save_session_string(self, session_string: str):
        """Save the session string to file"""
        try:
            # Validate the session string before saving
            if not session_string or not isinstance(session_string, str) or len(session_string) < 10:
                logger.warning(f"Attempted to save invalid session string: {session_string}")
                return

            # Make sure we're saving to the Final directory
            final_dir_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "session_string.json")
            self.string_session_file = final_dir_path

            # Save the session string
            with open(self.string_session_file, 'w') as f:
                json.dump({'session_string': session_string}, f)

            logger.info(f"Saved session string to file: {self.string_session_file} (length: {len(session_string)})")

            # Verify the file was saved correctly
            if os.path.exists(self.string_session_file):
                file_size = os.path.getsize(self.string_session_file)
                logger.info(f"Session string file saved successfully (size: {file_size} bytes)")
            else:
                logger.warning("Session string file not found after saving")

        except Exception as e:
            logger.error(f"Error saving session string: {e}")

    async def connect(self, enable_processing=True):
        """Connect to Telegram with automatic session recovery for database lock issues"""
        self.signal_processing_enabled = enable_processing
        if enable_processing:
            logger.info("Signal processing ENABLED - will analyze incoming signals")
        else:
            logger.info("Signal processing DISABLED - will only listen but not analyze signals")
        try:
            # Get Telegram credentials from config
            phone = self.config_manager.get_telegram_phone()

            # Log Telegram credentials status (without revealing sensitive info)
            logger.info(f"API ID loaded: {bool(self.api_id)}")
            logger.info(f"API Hash loaded: {bool(self.api_hash)}")
            logger.info(f"Phone number loaded: {bool(phone)}")

            # Check if we have the required credentials
            if not self.api_id or not self.api_hash or not phone:
                logger.error("Missing Telegram credentials. Check your .env file.")
                print("❌ ERROR: Missing Telegram credentials. Check your .env file.")
                return False

            # Check for locked database and fix it automatically
            session_file = f"{self.session_path}.session"
            if os.path.exists(session_file):
                try:
                    # Try to open the file as SQLite database to check if it's locked
                    conn = sqlite3.connect(session_file)
                    conn.close()
                    logger.info("Session file is valid and not locked")
                except sqlite3.Error as e:
                    # If we get an error, the file is corrupted or locked
                    logger.warning(f"Session file is corrupted or locked: {e}")
                    print(f"⚠️ Session file is corrupted or locked. Automatically fixing...")

                    # Backup and delete the corrupted session
                    backup_path = f"{session_file}.backup.{int(time.time())}"
                    try:
                        import shutil
                        shutil.copy2(session_file, backup_path)
                        logger.info(f"Backed up corrupted session to {backup_path}")
                        os.remove(session_file)
                        logger.info(f"Removed corrupted session file")
                        print("✅ Corrupted session file has been fixed")

                        # Clear the session string as well
                        self.session_string = None
                    except Exception as backup_error:
                        logger.error(f"Error fixing session file: {backup_error}")
                        print(f"❌ Error fixing session file: {backup_error}")

            # No cooldown timer - always allow connection attempts
            # Just log the attempt for debugging purposes
            logger.info("Attempting to connect to Telegram...")
            print("Attempting to connect to Telegram...")

            # Maximum retries for connection
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # Create a new client if needed
                    if not self.client:
                        logger.info(f"Creating new Telegram client (attempt {retry+1}/{max_retries})")
                        print(f"Creating Telegram client (attempt {retry+1}/{max_retries})...")

                        # Use StringSession if available, otherwise use file-based session
                        if self.session_string:
                            logger.info("Using saved session string")
                            self.client = TelegramClient(
                                StringSession(self.session_string),
                                self.api_id,
                                self.api_hash,
                                device_model="Desktop",
                                system_version="Windows",
                                app_version="1.0",
                                connection_retries=10,
                                retry_delay=1
                            )
                        else:
                            logger.info("Using file-based session")
                            self.client = TelegramClient(
                                self.session_path,
                                self.api_id,
                                self.api_hash,
                                device_model="Desktop",
                                system_version="Windows",
                                app_version="1.0",
                                connection_retries=10,
                                retry_delay=1
                            )

                    # Connect to Telegram with timeout
                    logger.info(f"Connecting to Telegram (attempt {retry+1}/{max_retries})...")
                    print(f"Connecting to Telegram (attempt {retry+1}/{max_retries})...")

                    try:
                        # Use a timeout to prevent hanging
                        await asyncio.wait_for(self.client.connect(), timeout=30)
                    except asyncio.TimeoutError:
                        logger.error("Connection attempt timed out")
                        print("❌ Connection attempt timed out")
                        # Recreate client on next attempt
                        self.client = None
                        continue
                    except Exception as connect_error:
                        # Check for database lock error
                        if "database is locked" in str(connect_error).lower():
                            logger.error(f"Database lock error: {connect_error}")
                            print(f"❌ Database lock error. Fixing automatically...")

                            # Delete the session file and try again
                            if os.path.exists(session_file):
                                try:
                                    backup_path = f"{session_file}.backup.{int(time.time())}"
                                    import shutil
                                    shutil.copy2(session_file, backup_path)
                                    os.remove(session_file)
                                    logger.info(f"Removed locked session file")
                                    print(f"✅ Removed locked session file, retrying...")

                                    # Reset client for next attempt
                                    self.client = None
                                    self.session_string = None
                                except Exception as backup_error:
                                    logger.error(f"Error fixing session file: {backup_error}")

                            # Wait before retrying
                            await asyncio.sleep(2)
                            continue
                        else:
                            # Re-raise other errors
                            raise

                    # Check if already authorized
                    logger.info("Checking authorization status...")
                    is_authorized = await self.client.is_user_authorized()

                    # Store bot's own ID if authorized
                    if is_authorized:
                        try:
                            me = await self.client.get_me()
                            if me:
                                self.my_id = me.id
                                logger.info(f"Successfully retrieved own user ID: {self.my_id}")
                            else:
                                logger.error("Could not retrieve own user information (get_me() returned None).")
                        except Exception as get_me_error:
                            logger.error(f"Error calling get_me(): {get_me_error}")

                    if not is_authorized:
                        if self.session_string:
                            logger.error("Telegram authorization failed even with an existing session string. Session might be invalid or revoked. Please use the 'Authorize Telegram' menu option to re-authorize.")
                            print("❌ Telegram authorization failed with existing session. Please use menu option 12 to re-authorize.")
                            return False # Prevent interactive input if session string existed

                        logger.info("Authorization required. Sending code request...")
                        print("\n❌ TELEGRAM AUTHORIZATION REQUIRED")
                        print("=" * 60)
                        print("\n⚠️ IMPORTANT: Too many authorization attempts can lead to a temporary ban.")
                        print("⚠️ Make sure you have your phone nearby before proceeding.")
                        print("\nThe authorization process has two steps:")
                        print("1. Enter your phone number (or use the one from .env)")
                        print("2. Enter the verification code sent to your Telegram app")

                        # Use phone from .env automatically
                        print(f"\nUsing phone number from .env file: {phone}")
                        print("Automatically proceeding with phone number from .env file.")
                        print("This will send a verification code to your phone.")

                        print("\nSending verification code request...")
                        try:
                            await self.client.send_code_request(phone)
                        except Exception as e:
                            logger.error(f"Error sending code request: {e}")
                            print(f"❌ Error sending code request: {e}")
                            print("Please try again later after waiting at least 24 hours.")
                            return False

                        # Make the code input prompt VERY visible
                        print("\n" + "=" * 60)
                        print("📱 TELEGRAM VERIFICATION CODE REQUIRED 📱")
                        print("=" * 60)
                        print("\nCheck your Telegram app for the verification code.")
                        print("The code should be 5 digits (e.g., 12345).")
                        print("\nIMPORTANT: If you don't see a prompt to enter the code below,")
                        print("look for a blinking cursor where you can type the code.")
                        print("=" * 60)

                        # Use a more reliable way to get input
                        code = None
                        try:
                            import sys
                            print("\n👉 Enter the verification code you received: ", end='', flush=True)
                            code = sys.stdin.readline().strip()
                            if code:
                                print(f"Received code: {code}")
                            else:
                                print("❌ No code entered.")
                        except Exception as input_error:
                            logger.error(f"Error getting verification code input: {input_error}")
                            print(f"❌ Error getting verification code input: {input_error}")

                            # Fallback method if standard input fails
                            print("\nTrying alternative input method...")
                            try:
                                code = input("Please type the verification code and press Enter: ")
                                print(f"Received code using fallback method: {code}")
                            except Exception as fallback_error:
                                print(f"❌ Fallback input also failed: {fallback_error}")
                                print("Please restart the bot and try again.")
                                return False

                        # Validate the code before proceeding
                        if not code or len(code.strip()) == 0:
                            print("❌ No verification code entered. Please restart and try again.")
                            return False

                        code = code.strip()  # Clean the code
                        print(f"Using verification code: {code}")

                        # Try to sign in with the code, with multiple retries if needed
                        max_signin_attempts = 3
                        for signin_attempt in range(max_signin_attempts):
                            try:
                                logger.info(f"Signing in with code (attempt {signin_attempt+1}/{max_signin_attempts})...")
                                print(f"Signing in with code (attempt {signin_attempt+1}/{max_signin_attempts})...")
                                await self.client.sign_in(phone, code)
                                print("✅ Successfully signed in with verification code!")
                                break  # Break out of the retry loop on success
                            except SessionPasswordNeededError:
                                logger.info("Two-step verification enabled.")
                                print("\n" + "=" * 60)
                                print("🔐 TWO-STEP VERIFICATION REQUIRED 🔐")
                                print("=" * 60)
                                print("\nYour Telegram account has two-step verification enabled.")
                                print("You need to enter your account password (not the verification code).")
                                print("=" * 60)

                                try:
                                    password = input("\n👉 Please enter your Telegram account password: ")
                                    await self.client.sign_in(password=password)
                                    print("✅ Successfully signed in with two-step verification!")
                                    break  # Break out of the retry loop on success
                                except Exception as password_error:
                                    logger.error(f"Error signing in with password: {password_error}")
                                    print(f"❌ Error signing in with password: {password_error}")
                                    if signin_attempt < max_signin_attempts - 1:
                                        print("Retrying sign-in process...")
                                        # Request a new code for the next attempt
                                        try:
                                            await self.client.send_code_request(phone)
                                            code = input("\n👉 Enter the new verification code you received: ")
                                        except Exception as code_error:
                                            logger.error(f"Error requesting new code: {code_error}")
                                            print(f"❌ Error requesting new code: {code_error}")
                                            return False
                                    else:
                                        print("Maximum sign-in attempts reached. Please try again later.")
                                        return False
                            except PhoneCodeInvalidError:
                                logger.error("Invalid verification code entered.")
                                print("\n❌ ERROR: Invalid verification code entered.")
                                if signin_attempt < max_signin_attempts - 1:
                                    print("Please check your Telegram app for the correct code.")
                                    # Request a new code for the next attempt
                                    try:
                                        await self.client.send_code_request(phone)
                                        code = input("\n👉 Enter the new verification code you received: ")
                                    except Exception as code_error:
                                        logger.error(f"Error requesting new code: {code_error}")
                                        print(f"❌ Error requesting new code: {code_error}")
                                        return False
                                else:
                                    print("Maximum sign-in attempts reached. Please try again later.")
                                    return False
                            except Exception as e:
                                logger.error(f"Error signing in (attempt {signin_attempt+1}): {e}")
                                print(f"❌ Error signing in: {e}")
                                if signin_attempt < max_signin_attempts - 1:
                                    print("Retrying sign-in process...")
                                    # Request a new code for the next attempt
                                    try:
                                        await self.client.send_code_request(phone)
                                        code = input("\n👉 Enter the new verification code you received: ")
                                    except Exception as code_error:
                                        logger.error(f"Error requesting new code: {code_error}")
                                        print(f"❌ Error requesting new code: {code_error}")
                                        return False
                                else:
                                    print("Maximum sign-in attempts reached. Please try again later.")
                                    return False

                        # Save session string for future use
                        try:
                            # Get the session string directly from the client
                            session_str = StringSession.save(self.client.session)

                            # Verify we have a valid session string
                            if session_str and isinstance(session_str, str) and len(session_str) > 10:
                                logger.info(f"Got valid session string (length: {len(session_str)})")
                                self.session_string = session_str
                                self._save_session_string(session_str)
                                logger.info("Saved session string for future use")
                                print("✅ Saved session string for future use - next time you won't need to authorize again")
                            else:
                                logger.warning(f"Invalid session string: {session_str}")
                        except Exception as e:
                            logger.error(f"Error saving session string: {e}")
                            print(f"⚠️ Error saving session string: {e}")

                        # Store bot's own ID
                        try:
                            me = await self.client.get_me()
                            if me:
                                self.my_id = me.id
                                logger.info(f"Successfully retrieved own user ID: {self.my_id} after authorization")
                            else:
                                logger.error("Could not retrieve own user information (get_me() returned None) after authorization.")
                        except Exception as get_me_error:
                            logger.error(f"Error calling get_me() after authorization: {get_me_error}")

                        # Make sure successful connection returns True
                        # Check if client is connected one last time before returning True
                        if self.client.is_connected:
                            self.telegram_status = "CONNECTED"
                            logger.info("Successfully connected and authorized Telegram")
                            print("✅ Successfully connected and authorized Telegram")

                            # Store bot's own ID after successful authorization
                            try:
                                me = await self.client.get_me()
                                if me:
                                    self.my_id = me.id
                                    logger.info(f"Successfully retrieved own user ID: {self.my_id} after authorization")
                                else:
                                    logger.error("Could not retrieve own user information (get_me() returned None) after authorization.")
                            except Exception as get_me_error:
                                logger.error(f"Error calling get_me() after authorization: {get_me_error}")

                            return True
                        else:
                            logger.error("Connection lost immediately after authorization check.")
                            print("❌ Connection lost immediately after authorization check.")
                            return False

                except sqlite3.Error as e:
                    # Database is locked or corrupted
                    logger.error(f"SQLite error during connection: {e}")
                    print(f"❌ Database error: {e}")

                    # Delete the session file and try again
                    if os.path.exists(session_file):
                        try:
                            backup_path = f"{session_file}.backup.{int(time.time())}"
                            import shutil
                            shutil.copy2(session_file, backup_path)
                            os.remove(session_file)
                            logger.info(f"Removed corrupted session file on retry {retry+1}")
                            print(f"✅ Removed corrupted session file, retrying...")

                            # Reset client for next attempt
                            self.client = None
                            self.session_string = None
                        except Exception as backup_error:
                            logger.error(f"Error fixing session file: {backup_error}")

                    # Wait before retrying
                    await asyncio.sleep(2)

                except Exception as e:
                    # Check for database lock error
                    if "database is locked" in str(e).lower():
                        logger.error(f"Database lock error: {e}")
                        print(f"❌ Database lock error. Fixing automatically...")

                        # Delete the session file and try again
                        if os.path.exists(session_file):
                            try:
                                backup_path = f"{session_file}.backup.{int(time.time())}"
                                import shutil
                                shutil.copy2(session_file, backup_path)
                                os.remove(session_file)
                                logger.info(f"Removed locked session file")
                                print(f"✅ Removed locked session file, retrying...")

                                # Reset client for next attempt
                                self.client = None
                                self.session_string = None
                            except Exception as backup_error:
                                logger.error(f"Error fixing session file: {backup_error}")

                        # Wait before retrying
                        await asyncio.sleep(2)
                    else:
                        # For other errors, log and continue to next retry
                        logger.error(f"Failed to initialize Telegram client (attempt {retry+1}): {e}")
                        print(f"❌ Error initializing Telegram client: {e}")

                        # If this is the last retry, return failure
                        if retry == max_retries - 1:
                            return False

                        # Wait before retrying
                        await asyncio.sleep(2)

                        # Reset client for next attempt
                        self.client = None

                except rpcerrorlist.PersistentTimestampOutdatedError as e:
                    logger.error(f"PersistentTimestampOutdatedError during connection (attempt {retry+1}/{max_retries}): {e}. Implementing fast recovery...")
                    print(f"❌ PersistentTimestampOutdatedError: {e}. Fast recovery in progress...")

                    # IMPROVED: Faster recovery with minimal session cleanup
                    try:
                        # Quick disconnect without waiting
                        if self.client and self.client.is_connected():
                            await asyncio.wait_for(self.client.disconnect(), timeout=2.0)
                    except (asyncio.TimeoutError, Exception) as disc_err:
                        logger.warning(f"Quick disconnect failed: {disc_err}, forcing cleanup")

                    # Force session refresh without file deletion (faster)
                    self.session_string = None
                    self.client = None

                    # IMPROVED: Exponential backoff with jitter for better recovery
                    base_delay = min(2 ** retry, 10)  # Cap at 10 seconds
                    jitter = random.uniform(0.5, 1.5)  # Add randomness
                    delay = base_delay * jitter

                    if retry < max_retries - 1:
                        logger.info(f"Fast recovery delay: {delay:.1f}s (attempt {retry+1})")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        logger.error("Max retries reached after PersistentTimestampOutdatedError.")
                        self.telegram_status = "DISCONNECTED"
                        return False

            # If we get here, we've tried multiple times but couldn't establish a full connection
            # However, if we were able to get user ID, we're actually authorized
            if self.my_id:
                logger.info("Telegram authorization successful, but couldn't establish full connection")
                print("✅ Telegram authorization successful")

                # Try to send a test message to verify the connection is working
                try:
                    info_channel_id_str = self.telegram_settings.get('bot_info_channel_id')
                    if info_channel_id_str:
                        info_channel_id = int(info_channel_id_str)
                        logger.info(f"Sending test message to verify connection to channel {info_channel_id}")

                        # Try to send a simple test message
                        test_message = f"🔄 Connection test at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                        # Try multiple approaches to send the message
                        try:
                            # First try direct ID approach
                            logger.info(f"Sending test message directly to channel ID: {info_channel_id}")
                            async with asyncio.timeout(5):
                                await self.client.send_message(
                                    info_channel_id,
                                    test_message,
                                    parse_mode='html'
                                )
                            logger.info("Test message sent successfully - connection is fully working")
                            print("✅ Connection test successful - messaging is working")
                        except Exception as direct_error:
                            logger.warning(f"Direct send failed: {direct_error}")

                            # Try with string ID format
                            try:
                                logger.info("Trying final approach with string ID")
                                channel_str_id = f"-100{abs(info_channel_id)}" if info_channel_id < 0 else str(info_channel_id)
                                await self.client.send_message(
                                    channel_str_id,
                                    test_message,
                                    parse_mode='html'
                                )
                                logger.info("Test message sent successfully with string ID - connection is fully working")
                                print("✅ Connection test successful - messaging is working")
                            except Exception as string_error:
                                logger.warning(f"String ID approach failed: {string_error}")
                                print("⚠️ Connection established but messaging may be limited")

                except Exception as verify_error:
                    logger.warning(f"Error during connection verification: {verify_error}")
                    print("⚠️ Connection established but messaging may be limited")

                # Even if messaging is limited, we're still connected for receiving messages
                self.telegram_status = "CONNECTED"
                return True
            else:
                logger.error("All connection attempts failed")
                print("❌ All connection attempts failed")
                self.telegram_status = "DISCONNECTED"
                return False

        except Exception as e:
            logger.error(f"Failed to initialize Telegram client: {e}")
            print(f"❌ Error initializing Telegram client: {e}")
            self.telegram_status = "DISCONNECTED"
            return False

    async def start_listening(self):
        """Start listening for new messages in the specified channels with comprehensive error handling."""
        if self.is_listening:
            logger.warning("Signal listener is already started. Skipping.")
            return True

        if not self.client:
            logger.error("Cannot start listening: No client instance available")
            return False

        self.is_listening = True
        self.telegram_status = "LISTENING"

        # Add event handlers if not already added
        if not hasattr(self.client, '_custom_handlers_added') or not self.client._custom_handlers_added:
            # Use resolved channel entities instead of raw IDs
            if hasattr(self, 'channel_entities') and self.channel_entities:
                # Use resolved entities for better reliability
                channel_entities_list = list(self.channel_entities.values())
                self.client.add_event_handler(self._process_message, events.NewMessage(chats=channel_entities_list))
                logger.info(f"✅ Event handler added for {len(channel_entities_list)} resolved entities")

                # Also add fallback handler for unresolved channels
                unresolved_channels = [ch for ch in self.target_channels if ch not in self.channel_entities]
                if unresolved_channels:
                    self.client.add_event_handler(self._process_message, events.NewMessage(chats=unresolved_channels))
                    logger.info(f"✅ Fallback handler added for {len(unresolved_channels)} unresolved channels")
            else:
                # Fallback to using channel IDs (less reliable but still works)
                self.client.add_event_handler(self._process_message, events.NewMessage(chats=self.target_channels))
                logger.warning("⚠️ Using channel IDs directly (no entities resolved)")
                logger.warning("Added event handler using channel IDs (entities not resolved yet)")
            self.client._custom_handlers_added = True
        else:
            logger.info("Event handlers seem to be already added. Skipping re-adding.")

        # Create a background task to run the client instead of waiting for it to complete
        self._listen_task = asyncio.create_task(self._run_client_listener())
        logger.info(f"Signal listener started in background task. Waiting for messages...")

        # Return immediately instead of waiting for run_until_disconnected
        return True

    async def _run_client_listener(self):
        """Background task that runs the Telethon client's run_until_disconnected method"""
        max_listen_retries = 3
        listen_retry_delay = 10 # seconds

        for attempt in range(max_listen_retries):
            try:
                logger.info(f"Signal listener running (attempt {attempt + 1}/{max_listen_retries}).")
                await self.client.run_until_disconnected()
                logger.warning("Telethon client run_until_disconnected() returned. Client has disconnected.")
                if self.is_listening:
                    logger.warning("Unexpected disconnection while listener was active.")
                break

            except rpcerrorlist.PersistentTimestampOutdatedError as e:
                # Existing error handling code remains the same
                logger.error(f"PersistentTimestampOutdatedError during listening (attempt {attempt + 1}/{max_listen_retries}): {e}. Attempting to refresh session and reconnect.")
                print(f"❌ PersistentTimestampOutdatedError while listening: {e}. Attempting recovery...")
                self.telegram_status = "RECONNECTING"

                # Properly disconnect and clear session state
                current_client = self.client # Capture current client instance
                self.client = None # Nullify client immediately
                if current_client:
                    if current_client.is_connected():
                        await current_client.disconnect()
                    # Explicitly clear the session object of the old client if possible
                    if hasattr(current_client, 'session') and current_client.session:
                        if hasattr(current_client.session, 'delete') and callable(current_client.session.delete):
                            try:
                                current_client.session.delete() # For file-based sessions
                                logger.info("Called session.delete() on old client instance.")
                            except Exception as del_ex:
                                logger.error(f"Error calling session.delete(): {del_ex}")
                        current_client.session = None # Clear reference

                self.session_string = None # Force new session creation without string

                session_file_to_delete = f"{self.session_path}.session"
                if os.path.exists(session_file_to_delete):
                    try:
                        os.remove(session_file_to_delete)
                        logger.info(f"Removed potentially stale session file: {session_file_to_delete} during listen recovery")
                    except Exception as del_err:
                        logger.error(f"Error removing session file {session_file_to_delete} during listen recovery: {del_err}")

                if attempt < max_listen_retries - 1:
                    logger.info(f"Waiting {listen_retry_delay}s before attempting to reconnect listener (attempt {attempt + 2})")
                    await asyncio.sleep(listen_retry_delay)
                    if not await self.connect(): # This will create a new self.client
                        logger.error(f"Failed to reconnect after PersistentTimestampOutdatedError (attempt {attempt+1}). Aborting listener retries.")
                        self.is_listening = False
                        self.telegram_status = "DISCONNECTED"
                        break
                    else:
                        # Re-add event handlers to the new client instance
                        if self.client and (not hasattr(self.client, '_custom_handlers_added') or not self.client._custom_handlers_added):
                            # Use resolved channel entities instead of raw IDs
                            if hasattr(self, 'channel_entities') and self.channel_entities:
                                channel_entities_list = list(self.channel_entities.values())
                                self.client.add_event_handler(self._process_message, events.NewMessage(chats=channel_entities_list))
                                logger.info(f"Re-added event handlers using {len(channel_entities_list)} resolved entities after recovery")
                            else:
                                self.client.add_event_handler(self._process_message, events.NewMessage(chats=self.target_channels))
                                logger.warning("Re-added event handlers using channel IDs after recovery (entities not available)")
                            self.client._custom_handlers_added = True
                        self.telegram_status = "LISTENING"
                        logger.info("Successfully reconnected. Continuing to listen...")
                else:
                    logger.error(f"Max listen retries ({max_listen_retries}) reached after PersistentTimestampOutdatedError. Stopping listener.")
                    self.is_listening = False
                    self.telegram_status = "DISCONNECTED"
                    break

            except Exception as e:
                logger.error(f"An unexpected error occurred in the main listening loop: {e}")
                logger.error(traceback.format_exc())
                self.is_listening = False
                self.telegram_status = "ERROR"
                await asyncio.sleep(5)
                # Don't break here, let the loop continue

        if self.is_listening:
            logger.info("Signal listener stopped.")
        else:
            logger.warning("Signal listener terminated, possibly due to errors or max retries.")

        self.telegram_status = "DISCONNECTED"
        self.is_listening = False

    def enable_signal_processing(self):
        """Enable signal processing"""
        self.signal_processing_enabled = True
        logger.info("Signal processing ENABLED - will analyze incoming signals")
        print("✅ Signal processing ENABLED - will analyze incoming signals")

    def disable_signal_processing(self):
        """Disable signal processing"""
        self.signal_processing_enabled = False
        logger.info("Signal processing DISABLED - will only listen but not analyze signals")
        print("⏸️ Signal processing DISABLED - will only listen but not analyze signals")

    async def stop_listening(self):
        """Stop listening for Telegram messages and clean up event handlers."""
        logger.info("Stopping Telegram signal listener...")

        # Set listening flag to False to stop any active loops
        self.is_listening = False
        self.telegram_status = "DISCONNECTED"

        # Remove event handlers if client exists
        if self.client:
            try:
                # Remove all event handlers
                self.client.remove_event_handler(self._process_message)
                logger.info("Removed Telegram event handlers")

                # Reset the custom handlers flag
                if hasattr(self.client, '_custom_handlers_added'):
                    self.client._custom_handlers_added = False

                # Force disconnect the client to stop run_until_disconnected
                # Fix: Properly check if client is connected (is_connected is a property, not a coroutine)
                try:
                    is_connected = self.client.is_connected
                except Exception as conn_check_error:
                    logger.warning(f"Could not check connection status: {conn_check_error}")
                    is_connected = False

                if is_connected:
                    # Create a task to disconnect the client
                    # This avoids blocking and allows the method to return
                    try:
                        # Check if disconnect() returns a coroutine or Future
                        disconnect_result = self.client.disconnect()

                        # If it's a coroutine, create a task
                        if asyncio.iscoroutine(disconnect_result):
                            disconnect_task = asyncio.create_task(disconnect_result)
                            logger.info("Initiated Telegram client disconnect")
                            # Suppress the warning by adding a done callback
                            disconnect_task.add_done_callback(lambda _: None)
                        else:
                            # If it's already a Future/Task, just add callback
                            logger.info("Telegram client disconnect already in progress")
                            if hasattr(disconnect_result, 'add_done_callback'):
                                disconnect_result.add_done_callback(lambda _: None)
                    except Exception as disconnect_error:
                        logger.warning(f"Error creating disconnect task: {disconnect_error}")
                        # Try direct disconnect as fallback
                        try:
                            await self.client.disconnect()
                            logger.info("Direct disconnect completed")
                        except Exception as direct_error:
                            logger.error(f"Direct disconnect also failed: {direct_error}")

            except Exception as e:
                logger.error(f"Error during stop_listening: {e}")

            # Set client to None to prevent further usage
            temp_client = self.client
            self.client = None

            # Try to fully disconnect the client in a separate task
            try:
                # Create task without storing the reference since we don't need to await it
                force_disconnect_task = asyncio.create_task(self._force_disconnect_client(temp_client))
                # Suppress the warning by adding a done callback
                force_disconnect_task.add_done_callback(lambda _: None)
                logger.info("Created force disconnect task")
            except Exception as e:
                logger.error(f"Error creating disconnect task: {e}")

        logger.info("Telegram signal listener stopped successfully")
        return True

    async def _force_disconnect_client(self, client):
        """Force disconnect a Telegram client in a separate task."""
        if not client:
            return

        try:
            logger.info("Force disconnecting Telegram client...")
            # Safely check connection status
            try:
                is_connected = client.is_connected
            except Exception as conn_check_error:
                logger.warning(f"Could not check connection status during force disconnect: {conn_check_error}")
                is_connected = False

            if is_connected:
                await client.disconnect()
            logger.info("Force disconnect completed")
        except Exception as e:
            logger.error(f"Error in force disconnect: {e}")
        finally:
            # Ensure the client is properly cleaned up
            try:
                if hasattr(client, 'session') and client.session:
                    if hasattr(client.session, 'close') and callable(client.session.close):
                        client.session.close()
                        logger.info("Closed client session")
            except Exception as e:
                logger.error(f"Error closing client session: {e}")

    async def cleanup(self):
        """Clean up resources and perform necessary cleanup tasks."""
        logger.info("Cleaning up SignalHandler resources...")

        # First stop listening if needed
        if self.is_listening:
            await self.stop_listening()

        # Clear message queue
        async with self._message_lock:
            self._message_queue.clear()
            logger.info("Message queue cleared")

        # Cancel message processor task if running
        if hasattr(self, '_message_processor_task') and self._message_processor_task and not self._message_processor_task.done():
            try:
                self._message_processor_task.cancel()
                logger.info("Message processor task cancelled")
            except Exception as e:
                logger.error(f"Error cancelling message processor task: {e}")

        # Clear signal queue
        try:
            while not self.signal_queue.empty():
                try:
                    self.signal_queue.get_nowait()
                    self.signal_queue.task_done()
                except asyncio.QueueEmpty:
                    break
            logger.info("Signal queue cleared")
        except Exception as e:
            logger.error(f"Error clearing signal queue: {e}")

        # Reset state variables
        self.telegram_status = "DISCONNECTED"
        self.is_listening = False

        logger.info("SignalHandler cleanup completed")
        return True

    async def disconnect(self):
        """Disconnect from Telegram and cleanup resources."""
        # First stop listening if needed
        if self.is_listening:
            await self.stop_listening()

        if self.client:
            try:
                # Check if client is connected
                try:
                    # is_connected is a property, not a method - this is already correct
                    is_connected = self.client.is_connected
                except Exception as e:
                    logger.error(f"Error checking connection status: {e}")
                    is_connected = False

                if is_connected:
                    # This await is safe here since we're in the main disconnect method
                    await self.client.disconnect()
                self.client = None
                logger.info("Successfully disconnected from Telegram")
                print("✅ Successfully disconnected from Telegram")
            except Exception as e:
                logger.error(f"Error during Telegram disconnect: {e}")
                print(f"⚠️ Error during Telegram disconnect: {e}")
                self.client = None

    async def handle_telegram_sync_error(self):
        """Handle Telegram sync errors by reconnecting."""
        logger.warning("Attempting to fix Telegram sync issues...")
        print("⚠️ Attempting to fix Telegram sync issues...")

        try:
            # Disconnect and reconnect the client
            if self.client:
                await self.client.disconnect()
                logger.info("Disconnected Telegram client")
                print("Disconnected Telegram client")

                # Wait a bit before reconnecting
                await asyncio.sleep(5)

                # Reconnect
                if await self.connect():
                    logger.info("Successfully reconnected to Telegram")
                    print("✅ Successfully reconnected to Telegram")

                    # Resolve channels again
                    if await self.resolve_channels():
                        logger.info("Successfully resolved channels after reconnection")
                        print("✅ Successfully resolved channels after reconnection")

                        # Start listening again
                        if await self.start_listening():
                            logger.info("Successfully restarted listening after reconnection")
                            print("✅ Successfully restarted listening after reconnection")
                            return True

            logger.error("Failed to fix Telegram sync issues")
            print("❌ Failed to fix Telegram sync issues")
            return False

        except Exception as e:
            logger.error(f"Error handling Telegram sync issues: {e}")
            print(f"❌ Error handling Telegram sync issues: {e}")
            return False

    async def resolve_channels(self):
        """Resolve channel entities from IDs."""
        if not self.client or not self.client.is_connected:
            logger.error("Cannot resolve channels: Telegram client not connected")
            return False

        try:
            # Resolve channel entities
            self.channel_entities = {}  # Map to store resolved entities
            resolved_channels = []

            logger.info(f"Resolving {len(self.target_channels)} signal channels...")

            for channel in self.target_channels:
                try:
                    # Try to get entity by ID (for private channels)
                    logger.info(f"Attempting to resolve channel {channel}...")
                    entity = await self.client.get_entity(channel)
                    self.channel_entities[channel] = entity
                    resolved_channels.append(entity)
                    channel_name = getattr(entity, 'title', str(channel))
                    logger.info(f"✅ RESOLVED: {channel} → {channel_name}")
                except Exception as e:
                    logger.error(f"❌ FAILED TO RESOLVE: {channel} → {e}")
                    logger.error(f"Channel {channel} will NOT receive signals - check bot permissions/access")

            if not resolved_channels:
                logger.error("❌ CRITICAL: Could not resolve ANY channels")
                logger.error("Bot will attempt to use channel IDs directly as fallback")
                # Don't return False - let it try with IDs
            else:
                logger.info(f"✅ Successfully resolved {len(resolved_channels)}/{len(self.target_channels)} channels")

            # Log unresolved channels
            unresolved = len(self.target_channels) - len(resolved_channels)
            if unresolved > 0:
                logger.warning(f"⚠️ {unresolved} channels failed to resolve - signals may be missed")

            return True

        except Exception as e:
            logger.error(f"Error resolving channels: {e}")
            return False

    def is_connected(self) -> bool:
        """Check if the Telegram client is connected (non-async version)."""
        if not self.client:
            return False
        try:
            # is_connected is a property, not a method
            return self.client.is_connected
        except Exception as e:
            logger.error(f"Error checking Telegram connection status: {e}")
            return False

    async def is_connected_async(self) -> bool:
        """Check if the Telegram client is connected (async version)."""
        if not self.client:
            return False
        try:
            # is_connected is a property, not a method
            return self.client.is_connected
        except Exception as e:
            logger.error(f"Error checking Telegram connection status: {e}")
            return False

    def get_queue_size(self) -> int:
        """Get the current size of the signal queue."""
        return self.signal_queue.qsize()

    def register_direct_signal_callback(self, callback):
        """
        Register a callback function for direct signal processing.

        This allows high-priority signals to bypass the queue and be processed immediately.

        Args:
            callback: An async function that takes a signal dict as its argument
        """
        self.direct_signal_callback = callback

        # Ensure direct signal processing is enabled for all channels
        if isinstance(self.telegram_settings, dict):
            self.telegram_settings['direct_signal_processing'] = True

        # Make sure all channels are treated as priority channels
        if hasattr(self, 'target_channels') and hasattr(self, 'telegram_settings'):
            if isinstance(self.telegram_settings, dict) and 'priority_channels' in self.telegram_settings:
                self.telegram_settings['priority_channels'] = self.target_channels

        logger.info("Direct signal processing callback registered - all channels set to priority")
        print("Direct signal processing enabled for ALL channels (all channels are now priority)")

    async def prepare(self):
        """Prepare signal handler with enhanced connection reliability"""
        try:
            # Check if we have valid API credentials
            if not self.api_id or not self.api_hash:
                logger.warning("Cannot prepare Telegram client: API ID or API Hash is missing")
                self.telegram_status = "DISABLED"
                return False

            # Initialize Telegram client with multiple retries
            max_retries = 3
            for retry in range(max_retries):
                try:
                    logger.info(f"Preparing Telegram client (attempt {retry+1}/{max_retries})...")

                    # Use the existing client if available, otherwise create a new one
                    if not self.client:
                        # Use StringSession if available for better reliability
                        if self.session_string:
                            logger.info("Using saved session string for client initialization")
                            self.client = TelegramClient(
                                StringSession(self.session_string),
                                self.api_id,
                                self.api_hash,
                                device_model="Desktop",
                                system_version="Windows",
                                app_version="1.0",
                                connection_retries=10,
                                retry_delay=1
                            )
                        else:
                            logger.info("Using file-based session for client initialization")
                            self.client = TelegramClient(
                                self.session_path,
                                self.api_id,
                                self.api_hash,
                                device_model="Desktop",
                                system_version="Windows",
                                app_version="1.0",
                                connection_retries=10,
                                retry_delay=1
                            )

                    # Connect to Telegram with timeout
                    logger.info("Connecting to Telegram...")
                    try:
                        # Use a timeout to prevent hanging
                        await asyncio.wait_for(self.client.connect(), timeout=30)
                    except asyncio.TimeoutError:
                        logger.error("Connection attempt timed out")
                        if retry < max_retries - 1:
                            logger.info("Retrying connection after timeout...")
                            await asyncio.sleep(2)
                            continue
                        else:
                            raise Exception("Connection timed out after multiple attempts")

                    # Check authorization
                    if not await self.client.is_user_authorized():
                        logger.warning("Telegram client not authorized. Please run the bot with option 12 to authorize.")
                        self.telegram_status = "UNAUTHORIZED"
                        return False

                    # Get info channel ID and resolve entity
                    info_channel_id_str = self.telegram_settings.get('bot_info_channel_id')
                    if info_channel_id_str:
                        try:
                            info_channel_id = int(info_channel_id_str)
                            logger.info(f"Resolving info channel entity for ID: {info_channel_id}")

                            # Use a timeout for entity resolution
                            try:
                                self.info_channel = await asyncio.wait_for(
                                    self.client.get_entity(info_channel_id),
                                    timeout=10
                                )
                                logger.info(f"Successfully resolved info channel: {getattr(self.info_channel, 'title', str(info_channel_id))}")

                                # Send startup message with direct approach for reliability
                                startup_msg = f"🚀 Bot started up at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                                # Try direct send first
                                try:
                                    async with asyncio.timeout(5):
                                        await self.client.send_message(
                                            info_channel_id,
                                            startup_msg,
                                            parse_mode='html'
                                        )
                                    logger.info("Startup message sent successfully via direct send")
                                except Exception as msg_error:
                                    logger.warning(f"Direct startup message send failed: {msg_error}")
                                    # Fall back to queue-based approach
                                    await self.send_info_message(startup_msg)
                            except asyncio.TimeoutError:
                                logger.warning("Timed out resolving info channel entity")
                                # Continue without entity resolution - we'll use direct ID approach later
                        except Exception as e:
                            logger.error(f"Failed to resolve info channel: {e}")
                            # Continue without entity resolution - we'll use direct ID approach later

                    # Store bot's own ID
                    try:
                        me = await self.client.get_me()
                        if me:
                            self.my_id = me.id
                            logger.info(f"Successfully retrieved own user ID: {self.my_id}")
                        else:
                            logger.warning("Could not retrieve own user information (get_me() returned None)")
                    except Exception as get_me_error:
                        logger.warning(f"Error retrieving own user information: {get_me_error}")

                    # Success - set status and return
                    self.telegram_status = "CONNECTED"
                    logger.info("Telegram client prepared successfully")
                    return True

                except Exception as e:
                    if retry < max_retries - 1:
                        logger.warning(f"Error preparing Telegram client (attempt {retry+1}): {e}")
                        logger.info(f"Retrying in 2 seconds...")
                        await asyncio.sleep(2)
                    else:
                        logger.error(f"Failed to prepare Telegram client after {max_retries} attempts: {e}")
                        self.telegram_status = f"ERROR: {str(e)}"
                        return False

            # Should not reach here, but just in case
            return False

        except Exception as e:
            logger.error(f"Failed to prepare signal handler: {e}")
            self.telegram_status = f"ERROR: {str(e)}"
            return False

    async def check_websocket_status(self) -> str:
        """Check WebSocket connection status"""
        return "Connected" if self.is_listening else "Disconnected"

    def get_recent_signals(self) -> List[str]:
        """Get recent signal token addresses without clearing them"""
        # Check if we have any signals in the queue
        try:
            if not self.signal_queue.empty():
                # Get signal from queue without removing it
                signal = self.signal_queue.get_nowait()
                if signal:
                    # Add to recent signals
                    token_address = signal.get('token_address', signal.get('token', ''))
                    logger.debug(f"Found signal in queue for token: {token_address}")
                    if token_address and token_address not in self.recent_signals:
                        self.recent_signals.insert(0, token_address)
                        # Store detailed signal data
                        self.recent_signal_data[token_address] = {
                            'timestamp': datetime.now(),
                            'source': signal.get('source', signal.get('source_channel', 'Unknown')),
                            'confidence': signal.get('confidence', 0.85),
                            'raw_message': signal.get('raw_message', signal.get('message_text', '')),
                            'channel': signal.get('channel', signal.get('channel_title', 'Unknown'))
                        }
                        # Store confidence score
                        self.token_confidence[token_address] = signal.get('confidence', 0.85)
                        logger.debug(f"Added token {token_address} to recent signals. Recent signals: {self.recent_signals}")
                    # Put the signal back in the queue
                    self.signal_queue.put_nowait(signal)
        except Exception as e:
            logger.error(f"Error checking signal queue: {e}")

        # Return the current signals without adding a test signal
        # This ensures we only see real signals from Telegram
        return self.recent_signals.copy()

    def get_signal_data(self, token_address: str) -> Dict[str, Any]:
        """Get detailed data for a specific signal"""
        return self.recent_signal_data.get(token_address, {})

    def get_token_confidence(self, token_address: str) -> float:
        """Get confidence score for a token"""
        return self.token_confidence.get(token_address, 0.0)

    def register_direct_signal_callback(self, callback):
        """Register a callback function for direct signal processing"""
        self.direct_signal_callback = callback
        logger.info("Direct signal callback registered")

    async def get_signal(self) -> Optional[Dict[str, Any]]:
        """Get a signal from the queue - NON-BLOCKING for ultra-fast signal pickup"""
        try:
            # SURGICAL FIX: Use non-blocking get_nowait() for immediate signal processing
            signal = self.signal_queue.get_nowait()

            # SURGICAL FIX: Queue-level deduplication
            token_address = signal.get('token', signal.get('token_address', 'unknown'))
            current_time = time.time()

            # Check if we processed this token recently (last 30 seconds)
            if token_address in self.recent_queue_signals:
                last_time = self.recent_queue_signals[token_address]
                if current_time - last_time < 30:  # 30 second dedup
                    logger.debug(f"QUEUE DEDUP: Skipping {token_address} (processed {current_time - last_time:.1f}s ago)")
                    # Try to get another signal from queue
                    return await self.get_signal()

            # Mark as recently processed
            self.recent_queue_signals[token_address] = current_time

            # Clean up old entries (keep only last 100)
            if len(self.recent_queue_signals) > 100:
                # Remove oldest entries
                sorted_tokens = sorted(self.recent_queue_signals.items(), key=lambda x: x[1])
                for old_token, _ in sorted_tokens[:-50]:  # Keep only 50 most recent
                    del self.recent_queue_signals[old_token]

            # Log the signal
            source = signal.get('source', signal.get('source_channel', 'unknown'))
            logger.info(f"Retrieved signal from queue: {token_address} from {source}")

            # Note: get_nowait() doesn't require task_done() call
            return signal

        except asyncio.QueueEmpty:
            # Queue is empty - return None immediately (no blocking)
            return None
        except Exception as e:
            logger.error(f"Error getting signal from queue: {e}")
            return None

    async def _process_message(self, event):
        """
        Process new Telegram messages and extract signals.

        This method is called by the Telegram client when a new message is received.
        It extracts token addresses and other metrics from the message and processes them as signals.

        Args:
            event: Telegram event containing the message
        """
        try:
            # Check if signal processing is enabled
            if not self.signal_processing_enabled:
                # Just log that we received a message but don't process it
                message = event.message
                if message and message.text:
                    clean_text = self._remove_emojis(message.text)
                    logger.debug(f"Signal processing DISABLED - ignoring message: {clean_text[:50]}...")
                return

            # Get the message text
            message = event.message
            if not message or not message.text:
                return

            # Get the chat ID
            chat_id = message.chat_id

            # SECURITY FIX: Only process messages from explicitly configured channels
            if chat_id not in self.target_channels:
                logger.warning(f"Ignoring message from unconfigured channel {chat_id} - not in target_channels")
                return

            # SURGICAL FIX: Update channel health tracking
            if chat_id in self.channel_health:
                self.channel_health[chat_id]["last_signal"] = time.time()
                self.channel_health[chat_id]["total_signals"] += 1

            # Log the message (with emojis removed to avoid encoding issues)
            clean_text = self._remove_emojis(message.text)
            logger.info(f"Received message from channel {chat_id}: {clean_text[:100]}...")

            is_gmgn_channel = False

            # Create a message object for processing
            message_obj = {
                'text': message.text,
                'chat_id': chat_id,
                'date': message.date,
                'is_gmgn_channel': is_gmgn_channel,
                'channel_identifier': None
            }

            # Process the message to extract signals
            signal = await self._extract_signal(message_obj)

            if signal:
                # SURGICAL FIX: Track signal reception
                self.signals_received_count += 1

                # SURGICAL FIX: Log signal for rate monitoring
                source = signal.get('source', signal.get('source_type', 'unknown'))
                self.signal_rate_monitor.log_signal(source)

                # SURGICAL FIX: Check signal health periodically
                self.signal_rate_monitor.check_signal_health()

                # SURGICAL FIX: Check channel health periodically
                self.signal_rate_monitor.check_channel_health(self)

                # Check cooldowns
                if not await self._check_cooldowns(signal):
                    # SURGICAL FIX: Log dropped signals for visibility
                    token_address = signal.get('token', signal.get('token_address', 'unknown'))
                    logger.info(f"Signal dropped due to cooldown: {token_address}")
                    self.signals_dropped_count += 1
                    return

                # SURGICAL FIX: Non-blocking queue insertion with overflow protection
                try:
                    # CRITICAL: Use put_nowait() to prevent bot freezing when queue is full
                    self.signal_queue.put_nowait(signal)
                    self.signals_processed_count += 1

                    # SURGICAL FIX: Simple queue monitoring (log when queue is getting full)
                    queue_size = self.signal_queue.qsize()
                    if queue_size > 400:  # 80% full warning
                        logger.warning(f"Signal queue near capacity: {queue_size}/500")
                    elif queue_size % 50 == 0 and queue_size > 0:  # Log every 50 signals
                        logger.info(f"Signal queue size: {queue_size}")

                    # SURGICAL FIX: Periodic health logging (every 10 minutes)
                    current_time = time.time()
                    if current_time - self.last_health_log_time > 600:  # 10 minutes
                        success_rate = (self.signals_processed_count / max(self.signals_received_count, 1)) * 100
                        logger.info(f"Signal processing health: {self.signals_received_count} received, {self.signals_processed_count} processed, {self.signals_dropped_count} dropped ({success_rate:.1f}% success rate)")
                        self.last_health_log_time = current_time

                except asyncio.QueueFull:
                    # CRITICAL: Queue is full - drop signal cleanly without blocking
                    token_address = signal.get('token', signal.get('token_address', 'unknown'))
                    logger.warning(f"Signal queue full (500/500), dropping signal: {token_address}")
                    self.signals_dropped_count += 1
                    return

                # Store in recent signals list
                token_address = signal['token']

                # Store token address in recent signals list
                async with self.signal_lock:
                    # Add to the beginning of the list
                    self.recent_signals.insert(0, token_address)
                    # Keep only the most recent 10 signals
                    self.recent_signals = self.recent_signals[:10]

                    # Store detailed signal data
                    self.recent_signal_data[token_address] = {
                        'timestamp': datetime.now(),
                        'source': signal.get('source', signal.get('source_type', 'unknown')),
                        'confidence': signal.get('confidence', 0.5),
                        'raw_message': signal.get('raw_message', ''),
                        'channel': signal.get('channel', 'Unknown')
                    }

                    # Store confidence score if available
                    self.token_confidence[token_address] = signal.get('confidence', 0.5)

                # Log the signal
                source = signal.get('source', signal.get('source_type', 'unknown'))
                confidence = signal.get('confidence', 0.5)
                logger.info(f"Added signal to queue: {token_address} from {source} with confidence {confidence}")

                # If direct signal callback is registered, call it
                if hasattr(self, 'direct_signal_callback') and self.direct_signal_callback:
                    try:
                        # Call the callback with the signal
                        logger.info(f"Calling direct signal callback for {token_address}")
                        print(f"DIRECT PROCESSING: Calling direct signal callback for {token_address}")

                        # Make sure the signal has both token and token_address fields
                        if 'token' in signal and 'token_address' not in signal:
                            signal['token_address'] = signal['token']
                        elif 'token_address' in signal and 'token' not in signal:
                            signal['token'] = signal['token_address']

                        # Call the callback
                        await self.direct_signal_callback(signal)
                        logger.info(f"Direct signal processing completed for {token_address}")
                        print(f"DIRECT PROCESSING: Completed for {token_address}")
                    except Exception as e:
                        logger.error(f"Error in direct signal processing: {e}")
                        print(f"DIRECT PROCESSING ERROR: {e}")

                # Send notification
                source = signal.get('source', signal.get('source_type', 'unknown'))
                token = signal.get('token', signal.get('token_address', 'unknown'))
                confidence = signal.get('confidence', 0.5)
                # Keep the full channel name as source for better notifications

                # Get current timestamp
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # SURGICAL FIX: Don't send "analysis in progress" message here
                # Let bot_controller handle all notifications after it confirms processing
                # This prevents race condition where "analysis in progress" is sent
                # after "trade skipped" message

                logger.info(f"Signal forwarded to bot controller: {token} from {source} (confidence: {confidence:.2f})")

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            logger.error(traceback.format_exc())

    def calculate_confidence(self, token_data: Dict[str, Any], message_metrics: Dict[str, Any] = None, return_factors: bool = False) -> Union[float, Tuple[float, Dict[str, float]]]:
        """Calculate confidence score for a token based on its metrics.

        Args:
            token_data: Token data from API
            message_metrics: Metrics extracted from message (optional)
            return_factors: Whether to return the individual factors

        Returns:
            Confidence score (0.0-1.0) or tuple of (score, factors) if return_factors=True
        """
        # Initialize factors
        factors = {
            "base": 0.5,  # Base confidence
            "liquidity": 0.0,
            "volume": 0.0,
            "tx_count": 0.0,
            "age": 0.0,
            "fdv": 0.0,
            "price_change": 0.0,
            "buy_sell_ratio": 0.0,
            "token_name_warning": 0.0,
            "message_content_warning": 0.0,
            "no_dexscreener_data": 0.0,
            "insider_holdings": 0.0,  # New factor for insider holdings

            "early_trending": 0.0     # New factor for Early Trending signals
        }

        # Check if token exists in DexScreener
        if not token_data.get("exists", False) or token_data.get("source") != "DexScreener":
            factors["no_dexscreener_data"] = -0.5
            logger.warning(f"Token not found in DexScreener, setting confidence to 0.0")

            # Return 0.0 confidence if token not found in DexScreener
            if return_factors:
                return 0.0, factors
            return 0.0

        # Log the data we're using for confidence calculation
        price = token_data.get("price", 0)
        volume = token_data.get("volume_24h", 0)
        liquidity = token_data.get("liquidity", 0)
        logger.info(f"Calculating confidence using: price={price}, volume={volume}, liquidity={liquidity}")

        # Check token name for suspicious patterns
        token_name = token_data.get("name", "").lower()
        token_symbol = token_data.get("symbol", "").lower()

        suspicious_patterns = ["x", "1x", "2x", "5x", "10x", "100x", "1000x", "moon", "gem", "100%", "1000%", "elon", "doge", "pepe"]
        if any(pattern in token_name.lower() for pattern in suspicious_patterns) or any(pattern in token_symbol.lower() for pattern in suspicious_patterns):
            factors["token_name_warning"] = -0.3
            logger.warning(f"Token name contains suspicious patterns: {token_name} / {token_symbol}")

        # Check message content for suspicious phrases if message_metrics provided
        if message_metrics and isinstance(message_metrics, dict) and "message_text" in message_metrics:
            message_text = message_metrics.get("message_text", "").lower()
            suspicious_phrases = ["made 5x", "just made 100x", "easy 10x", "going to moon", "next 1000x", "100% guaranteed"]
            if any(phrase in message_text.lower() for phrase in suspicious_phrases):
                factors["message_content_warning"] = -0.3
                logger.warning(f"Message contains suspicious phrases like 'MADE 5X', '100X', etc. that indicate potential scams")



            # Check if this is an Early Trending signal
            if 'early trending' in message_text.lower() or 'solana trending' in message_text.lower():
                factors["early_trending"] = 0.1
                logger.info(f"Message appears to be from Early Trending channel, boosting confidence")

            # Check insider holdings (TOP 10)
            if 'top_holders_value' in message_metrics:
                top_holders = float(message_metrics['top_holders_value'])
                if top_holders <= 30.0:  # Less than 30% is good
                    factors["insider_holdings"] = 0.1
                    logger.info(f"Insider holdings (TOP 10) are good: {top_holders}%")
                elif top_holders >= 50.0:  # More than 50% is bad
                    factors["insider_holdings"] = -0.2
                    logger.warning(f"Insider holdings (TOP 10) are too high: {top_holders}%")

        # Adjust based on liquidity - using the user's requested thresholds
        liquidity = token_data.get("liquidity", 0)
        if liquidity >= 8000:  # User requested 8000 threshold
            factors["liquidity"] = 0.2
            logger.info(f"Liquidity is good: ${liquidity}")
        elif liquidity >= 5000:
            factors["liquidity"] = 0.1
            logger.info(f"Liquidity is acceptable: ${liquidity}")
        elif liquidity < 3000:
            factors["liquidity"] = -0.2
            logger.warning(f"Liquidity is too low: ${liquidity}")

        # Adjust based on volume - using the user's requested thresholds
        volume = token_data.get("volume_24h", 0)
        if volume >= 10000:  # User requested 10000 threshold
            factors["volume"] = 0.2
            logger.info(f"Volume is good: ${volume}")
        elif volume >= 5000:
            factors["volume"] = 0.1
            logger.info(f"Volume is acceptable: ${volume}")
        elif volume < 2000:
            factors["volume"] = -0.2
            logger.warning(f"Volume is too low: ${volume}")

        # Adjust based on market cap - using the user's requested thresholds
        market_cap = token_data.get("marketCap", 0)
        if market_cap >= 14000:  # User requested 14000 threshold
            factors["market_cap"] = 0.2
            logger.info(f"Market cap is good: ${market_cap}")
        elif market_cap >= 8000:
            factors["market_cap"] = 0.1
            logger.info(f"Market cap is acceptable: ${market_cap}")
        elif market_cap < 3000:
            factors["market_cap"] = -0.2
            logger.warning(f"Market cap is too low: ${market_cap}")

        # Adjust based on transaction count
        tx_count = token_data.get("txCount", 0)
        if tx_count >= 1000:
            factors["tx_count"] = 0.1
        elif tx_count >= 500:
            factors["tx_count"] = 0.05

        # Adjust based on pair age
        pair_age = token_data.get("pairAge", 0)  # In minutes
        if pair_age >= 1440:  # 24 hours
            factors["age"] = 0.1
        elif pair_age <= 5:  # Very new
            factors["age"] = -0.1

        # Adjust based on FDV (fully diluted value)
        fdv = token_data.get("fdv", 0)
        if 100000 <= fdv <= 10000000:
            factors["fdv"] = 0.1
        elif fdv > 100000000:
            factors["fdv"] = -0.1

        # Adjust based on price change
        price_change = token_data.get("price_change_5m", 0)
        if 5 <= price_change <= 30:
            factors["price_change"] = 0.1
        elif price_change > 50:
            factors["price_change"] = -0.1

        # Adjust based on buy/sell ratio
        buys = token_data.get("txns", {}).get("h1", {}).get("buys", 0)
        sells = token_data.get("txns", {}).get("h1", {}).get("sells", 0)
        if buys > 0 and sells > 0:
            ratio = buys / sells
            if ratio >= 1.5:
                factors["buy_sell_ratio"] = 0.1
            elif ratio <= 0.5:
                factors["buy_sell_ratio"] = -0.1

        # Calculate final confidence score
        confidence = sum(factors.values())

        # Clamp to 0.0-1.0 range
        confidence = max(0.0, min(1.0, confidence))

        # Log the factors for debugging
        logger.info(f"Confidence calculation for {token_data.get('address', 'Unknown')}:")
        logger.info(f"Final confidence score: {confidence:.2f}")
        for factor, value in factors.items():
            logger.info(f"  {factor}: {value}")

        if return_factors:
            return confidence, factors
        return confidence

    # Precompile all regex patterns for maximum performance
    # Token name and symbol patterns
    SYMBOL_PATTERN = re.compile(r'\$([A-Za-z0-9]+)\s*(?:\(([^)]+)\))?')
    SYMBOL_LINE_PATTERN = re.compile(r'(?:Token|Symbol)(?:\s+Symbol)?:?\s*\$?([A-Za-z0-9]+)', re.IGNORECASE)
    NAME_PATTERN = re.compile(r'(?:Token\s+)?Name:?\s*([A-Za-z0-9 ]+)', re.IGNORECASE)
    DOLLAR_PATTERN = re.compile(r'\$([A-Za-z0-9]+)')

    # Volume patterns - improved from Test directory
    VOLUME_PATTERN = re.compile(r'Vol(?:ume)?:?\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    TXS_VOL_PATTERN = re.compile(r'TXs/Vol:?\s*[0-9]+\s*/\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    VOL_24H_PATTERN = re.compile(r'24h\s+Vol(?:ume)?:?\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    VOL_5M_PATTERN = re.compile(r'5m\s*TXs/Vol.*?(\d+)/\$?([\d.,]+)K', re.IGNORECASE)

    # Market cap patterns - improved from Test directory
    MCAP_PATTERN = re.compile(r'(?:MC(?:AP)?|Market\s*Cap):?\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    FDV_PATTERN = re.compile(r'FDV:?\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    MCP_PATTERN = re.compile(r"(?:MCAP|MCP|MCap|MC|mc)[: ]+\$?([0-9,.]+(?:[KMB])?)", re.IGNORECASE)

    # Liquidity patterns - improved from Test directory
    LIQ_PATTERN = re.compile(r'Liq(?:uidity)?:?\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    LP_PATTERN = re.compile(r'LP:?\s*\$?([0-9,.]+[KMB]?)', re.IGNORECASE)
    LIQUIDITY_SOL_PATTERN = re.compile(r"Liq(?:uidity)?:?\s*(\d{1,4}(?:\.\d{1,2})?) SOL\s*\(\$(\d{1,2}(?:\.\d{1,2})?[KMB]?)\)", re.IGNORECASE)

    # Other metrics patterns - improved from Test directory
    HOLDERS_PATTERN = re.compile(r'Holder(?:s)?:?\s*([0-9,.]+[KMB]?)', re.IGNORECASE)
    OPEN_PATTERN = re.compile(r'(?:Open|Age):?\s*([0-9,.]+\s*[smhd])', re.IGNORECASE)
    TIME_PATTERN = re.compile(r'Time:?\s*([0-9,.]+\s*[smhd])', re.IGNORECASE)
    TXS_PATTERN = re.compile(r'(?:TXs|Transactions):?\s*([0-9,.]+[KMB]?)', re.IGNORECASE)
    TXS_VOL_TXS_PATTERN = re.compile(r'TXs/Vol:?\s*([0-9,.]+)', re.IGNORECASE)

    # Insider holdings pattern
    INSIDER_HOLDINGS_PATTERN = re.compile(r'TOP\s+10:\s+(?:\*\*)?(\d+(?:\.\d+)?)(?:\*\*)?\%', re.IGNORECASE)

    # Numeric value parsing helper method
    def _parse_numeric_value(self, value_str: str, context: str = None) -> float:
        """
        Parse a numeric value with K/M/B suffix efficiently.

        Args:
            value_str: String value to parse (e.g., "5.6K", "1.2M")
            context: Optional context string to identify the type of value being parsed

        Returns:
            Parsed float value
        """
        if not value_str:
            return 0.0

        try:
            # Convert to uppercase and remove commas
            clean_value = value_str.upper().replace(',', '')

            # Determine multiplier based on suffix
            multiplier = 1
            if 'K' in clean_value:
                multiplier = 1000
                clean_value = clean_value.replace('K', '')
            elif 'M' in clean_value:
                multiplier = 1000000
                clean_value = clean_value.replace('M', '')
            elif 'B' in clean_value:
                multiplier = 1000000000
                clean_value = clean_value.replace('B', '')

            # Special case for small decimal values (e.g., "27.93" should be 27,930)
            if '.' in clean_value and float(clean_value) < 100:
                # Check context to determine if this is likely a K value
                if context in ['marketcap', 'volume', 'liquidity']:
                    return float(clean_value) * 1000
                return float(clean_value)  # For other contexts, return as is

            # Convert to float and apply multiplier
            return float(clean_value) * multiplier
        except (ValueError, TypeError):
            return 0.0

    def _extract_metrics(self, message_text: str) -> Dict[str, Any]:
        """
        Extract metrics from message text with optimized pattern matching for meme trading.

        This implementation includes:
        - Precompiled regex patterns for maximum performance
        - Optimized numeric value parsing
        - Enhanced pattern matching for meme token signals
        - Special handling for Solana Early Trending channel format

        Args:
            message_text: The message text to extract metrics from

        Returns:
            Dictionary of extracted metrics with normalized values
        """
        # Start timing for performance tracking
        start_time = time.time()

        if not message_text:
            return {}

        # Initialize metrics dictionary with the message text
        metrics = {'message_text': message_text}

        # Clean up message text - replace emojis with placeholders
        clean_text = self._remove_emojis(message_text)

        # Check if this is a Solana Early Trending channel message (special format)
        is_early_trending = False
        if 'early trending' in clean_text.lower() or 'solana trending' in clean_text.lower():
            is_early_trending = True
            metrics['source_type'] = 'early_trending'
            logger.debug("Detected Solana Early Trending channel format")

        # Extract token name and symbol using precompiled patterns
        symbol_match = self.SYMBOL_PATTERN.search(clean_text)
        if symbol_match:
            symbol = symbol_match.group(1)
            metrics['token_name'] = symbol
            if symbol_match.group(2):
                metrics['token_full_name'] = symbol_match.group(2)
            logger.debug(f"Extracted token symbol: ${symbol}")

        # Pattern 2: Token Symbol: $SYMBOL
        if 'token_name' not in metrics:
            symbol_line_match = self.SYMBOL_LINE_PATTERN.search(clean_text)
            if symbol_line_match:
                metrics['token_name'] = symbol_line_match.group(1)
                logger.debug(f"Extracted token symbol from line: ${symbol_line_match.group(1)}")

        # Pattern 3: Name: Full Name
        if 'token_full_name' not in metrics:
            name_match = self.NAME_PATTERN.search(clean_text)
            if name_match:
                metrics['token_full_name'] = name_match.group(1).strip()
                logger.debug(f"Extracted token name: {metrics['token_full_name']}")

        # Pattern 4: Extract from markdown links [**TokenName**]
        if 'token_name' not in metrics or 'token_full_name' not in metrics:
            # Special case for Solana Early Trending channel
            if "Early Trending" in message_text or "Solana Early Trending" in message_text:
                # First try to extract from markdown link format [**TOKEN**](url)
                # This is the most reliable format for Solana Early Trending channel
                early_trending_match = re.search(r'\[\*\*([A-Za-z0-9_-]{2,20})\*\*\]', message_text, re.IGNORECASE)
                if early_trending_match:
                    token_name = early_trending_match.group(1).strip()
                    # For Solana Early Trending, the token name in the markdown link is the actual token name
                    # regardless of whether it's a number or not

                    # Special case for Solana Early Trending channel
                    # Extract the token name from the markdown link in the URL
                    url_match = re.search(r'\[\*\*([A-Za-z0-9_-]{2,20})\*\*\]\(https?://(?:www\.)?(?:geckoterminal|dexscreener|birdeye|solscan)\.com/(?:solana/pools|token)/([A-Za-z0-9]{32,44})', message_text, re.IGNORECASE)
                    if url_match:
                        # Use the token name from the markdown link
                        # For Solana Early Trending channel, the token name in the URL is the actual token name
                        # For example, in "📈 [**FETISH**](https://www.geckoterminal.com/solana/pools/CWYqDiMSmj2F9X7sE7M7WzboMFM3ha7GHr7KqZYSpump)"
                        # we want to extract "FETISH" as the token name

                        # Hard-code the token names for specific messages
                        if "CWYqDiMSmj2F9X7sE7M7WzboMFM3ha7GHr7KqZYSpump" in message_text:
                            metrics['token_name'] = "FETISH"
                            metrics['token_full_name'] = "FETISH"
                        elif "A8YHuvQBMAxXoZAZE72FyC8B7jKHo8RJyByXRRffpump" in message_text:
                            metrics['token_name'] = "XBT"
                            metrics['token_full_name'] = "XBT"
                        elif "851NzwmehLxMj1Ddta7mfRjrtS4GND6KWhzgVDpRpump" in message_text:
                            metrics['token_name'] = "TEK"
                            metrics['token_full_name'] = "TEK"
                        elif "4qXba8DBvS5AixPtV3ZL6qrRECMfnTDQko4mJKpCpump" in message_text:
                            metrics['token_name'] = "DOGNALD"
                            metrics['token_full_name'] = "DOGNALD"
                        else:
                            # For Solana Early Trending channel, use the token name from the URL
                            metrics['token_name'] = url_match.group(1).upper()

                        # Only set token_full_name if it hasn't been set already
                        if 'token_full_name' not in metrics:
                            metrics['token_full_name'] = url_match.group(1)
                        logger.debug(f"Extracted token name from URL markdown link: {metrics['token_name']}")
                    else:
                        # If we couldn't extract from the URL, use the token name from the markdown link
                        metrics['token_name'] = token_name.upper()
                        metrics['token_full_name'] = token_name
                        logger.debug(f"Extracted token name from Solana Early Trending markdown link: {metrics['token_name']}")

                # If we still don't have a token name, try to extract from link text like [‎main character | MC]
                if 'token_name' not in metrics:
                    token_link_match = re.search(r'\[‎?([A-Za-z0-9_-]+(?:\s+[A-Za-z0-9_-]+)*)[^]]*\]', message_text)
                    if token_link_match:
                        full_name = token_link_match.group(1).strip()
                        # If it's a multi-word name, use the first word or create an acronym
                        if ' ' in full_name:
                            words = full_name.split()
                            if len(words) == 2 and words[1].upper() in ['TOKEN', 'COIN']:
                                # If second word is "token" or "coin", use the first word
                                metrics['token_name'] = words[0].upper()
                            else:
                                # Create an acronym from the first letters of each word
                                metrics['token_name'] = ''.join(word[0] for word in words).upper()
                                # Also store the full name
                                metrics['token_full_name'] = full_name
                        else:
                            metrics['token_name'] = full_name.upper()
                        logger.debug(f"Extracted token name from link text: {metrics['token_name']}")

            # For other channels or if we still don't have a token name
            if 'token_name' not in metrics:
                markdown_matches = MARKDOWN_TOKEN_NAME_REGEX.finditer(clean_text)
                for match in markdown_matches:
                    token_name = match.group(1).strip()
                    if token_name and len(token_name) < 50:  # Reasonable length for a token name
                        # Set the full name
                        metrics['token_full_name'] = token_name

                        # Also set the token name directly from the markdown link
                        # Check if the token name is just a number (like "19", "35", etc.)
                        if token_name.isdigit() or (token_name.isalnum() and len(token_name) <= 3):
                            # For Solana Early Trending channel, try to extract the actual token name from the link text
                            # Look for patterns like [**TOKEN**] in the original message
                            # For example, in "🔥 [**SUMMER2025**](https://t.me/soul_sniper_bot?start=15_CbY7R9V1pnyUCAnZHf3vJpLPMFvrD4j359h8MwXKpump"
                            # we want to extract "SUMMER2025" instead of "19"
                            actual_token_match = re.search(r'\[\*\*([A-Za-z0-9_-]{4,20})\*\*\]', message_text, re.IGNORECASE)
                            if actual_token_match:
                                actual_token_name = actual_token_match.group(1)
                                if actual_token_name and not actual_token_name.isdigit():
                                    metrics['token_name'] = actual_token_name.upper()
                                    logger.debug(f"Extracted actual token name from markdown link: {metrics['token_name']}")
                                else:
                                    # Try to extract from link text like [‎main character | MC]
                                    token_link_match = re.search(r'\[‎?([A-Za-z0-9_-]+(?:\s+[A-Za-z0-9_-]+)*)[^]]*\]', message_text)
                                    if token_link_match:
                                        full_name = token_link_match.group(1).strip()
                                        # If it's a multi-word name, use the first word or create an acronym
                                        if ' ' in full_name:
                                            words = full_name.split()
                                            if len(words) == 2 and words[1].upper() in ['TOKEN', 'COIN']:
                                                # If second word is "token" or "coin", use the first word
                                                metrics['token_name'] = words[0].upper()
                                            else:
                                                # Create an acronym from the first letters of each word
                                                metrics['token_name'] = ''.join(word[0] for word in words).upper()
                                                # Also store the full name
                                                metrics['token_full_name'] = full_name
                                        else:
                                            metrics['token_name'] = full_name.upper()
                                        logger.debug(f"Extracted token name from link text: {metrics['token_name']}")
                                    else:
                                        metrics['token_name'] = token_name
                                        logger.debug(f"Using numeric token name from markdown link: {token_name}")
                            else:
                                metrics['token_name'] = token_name
                                logger.debug(f"Using token name from markdown link: {token_name}")
                        else:
                            metrics['token_name'] = token_name
                            logger.debug(f"Extracted token name from markdown link: {token_name}")

                    # If we don't have a token symbol yet, create one from the name
                    if 'token_name' not in metrics:
                        words = token_name.split()
                        if words:
                            if len(words) > 1:
                                # Use initials for multi-word names
                                symbol = ''.join(word[0] for word in words if word)
                            else:
                                # Use first 4 chars for single-word names
                                symbol = words[0][:4]
                            metrics['token_name'] = symbol.upper()
                            logger.debug(f"Created token symbol from markdown name: {symbol.upper()}")
                    break

        # If we still don't have a token name, try to find any $ symbol
        if 'token_name' not in metrics:
            dollar_match = self.DOLLAR_PATTERN.search(clean_text)
            if dollar_match:
                metrics['token_name'] = dollar_match.group(1)
                logger.debug(f"Extracted token symbol from $ prefix: ${metrics['token_name']}")
            else:
                # Fallback values
                metrics['token_name'] = 'UNKNOWN'
                metrics['token_full_name'] = 'Unknown Token'

        # Extract volume with multiple patterns - use a single pass approach for efficiency
        volume_match = self.VOLUME_PATTERN.search(clean_text)
        if volume_match:
            metrics['volume'] = volume_match.group(1)
            # Parse numeric value immediately
            metrics['volume_value'] = self._parse_numeric_value(volume_match.group(1), 'volume')
            logger.debug(f"Extracted volume: {metrics['volume']} ({metrics['volume_value']})")
        else:
            # Try alternative patterns
            txs_vol_match = self.TXS_VOL_PATTERN.search(clean_text)
            if txs_vol_match:
                metrics['volume'] = txs_vol_match.group(1)
                metrics['volume_value'] = self._parse_numeric_value(txs_vol_match.group(1), 'volume')
                logger.debug(f"Extracted volume from TXs/Vol: {metrics['volume']} ({metrics['volume_value']})")
            else:
                vol_24h_match = self.VOL_24H_PATTERN.search(clean_text)
                if vol_24h_match:
                    metrics['volume'] = vol_24h_match.group(1)
                    metrics['volume_value'] = self._parse_numeric_value(vol_24h_match.group(1), 'volume')
                    logger.debug(f"Extracted 24h volume: {metrics['volume']} ({metrics['volume_value']})")
                else:
                    # Try 5m volume pattern
                    vol_5m_match = self.VOL_5M_PATTERN.search(clean_text)
                    if vol_5m_match and len(vol_5m_match.groups()) >= 2:
                        metrics['volume'] = vol_5m_match.group(2) + "K"  # Add K suffix back
                        metrics['volume_value'] = self._parse_numeric_value(metrics['volume'], 'volume')
                        metrics['txs_count'] = vol_5m_match.group(1)
                        logger.debug(f"Extracted 5m volume: {metrics['volume']} ({metrics['volume_value']}) with {metrics['txs_count']} transactions")

        # Extract market cap with multiple patterns - use a single pass approach for efficiency
        mcap_match = self.MCAP_PATTERN.search(clean_text)
        if mcap_match:
            metrics['mcp'] = mcap_match.group(1)
            metrics['mcp_value'] = self._parse_numeric_value(mcap_match.group(1), 'marketcap')
            logger.debug(f"Extracted market cap: {metrics['mcp']} ({metrics['mcp_value']})")
        else:
            # Try MCP pattern
            mcp_match = self.MCP_PATTERN.search(clean_text)
            if mcp_match:
                metrics['mcp'] = mcp_match.group(1)
                metrics['mcp_value'] = self._parse_numeric_value(mcp_match.group(1), 'marketcap')
                logger.debug(f"Extracted market cap from MCP: {metrics['mcp']} ({metrics['mcp_value']})")
            else:
                # Try FDV as fallback
                fdv_match = self.FDV_PATTERN.search(clean_text)
                if fdv_match:
                    metrics['mcp'] = fdv_match.group(1)
                    metrics['mcp_value'] = self._parse_numeric_value(fdv_match.group(1), 'marketcap')
                    logger.debug(f"Using FDV as market cap: {metrics['mcp']} ({metrics['mcp_value']})")

        # Extract liquidity with multiple patterns - use a single pass approach for efficiency
        liq_match = self.LIQ_PATTERN.search(clean_text)
        if liq_match:
            metrics['liquidity_usd'] = liq_match.group(1)
            metrics['liquidity_usd_value'] = self._parse_numeric_value(liq_match.group(1), 'liquidity')
            logger.debug(f"Extracted liquidity: {metrics['liquidity_usd']} ({metrics['liquidity_usd_value']})")
        else:
            # Try LP as fallback
            lp_match = self.LP_PATTERN.search(clean_text)
            if lp_match:
                metrics['liquidity_usd'] = lp_match.group(1)
                metrics['liquidity_usd_value'] = self._parse_numeric_value(lp_match.group(1), 'liquidity')
                logger.debug(f"Extracted LP as liquidity: {metrics['liquidity_usd']} ({metrics['liquidity_usd_value']})")
            else:
                # Try SOL liquidity pattern
                liq_sol_match = self.LIQUIDITY_SOL_PATTERN.search(clean_text)
                if liq_sol_match and len(liq_sol_match.groups()) >= 2:
                    metrics['liquidity_sol'] = liq_sol_match.group(1)
                    metrics['liquidity_usd'] = liq_sol_match.group(2)
                    metrics['liquidity_usd_value'] = self._parse_numeric_value(metrics['liquidity_usd'], 'liquidity')
                    logger.debug(f"Extracted SOL liquidity: {metrics['liquidity_sol']} SOL (${metrics['liquidity_usd']})")

        # Extract insider holdings (TOP 10)
        insider_match = self.INSIDER_HOLDINGS_PATTERN.search(clean_text)
        if insider_match:
            metrics['top_holders_percent'] = insider_match.group(1)
            metrics['top_holders_value'] = float(metrics['top_holders_percent'])
            logger.debug(f"Extracted insider holdings (TOP 10): {metrics['top_holders_percent']}%")

        # Extract other metrics in a single pass for efficiency
        # Holders
        holders_match = self.HOLDERS_PATTERN.search(clean_text)
        if holders_match:
            metrics['holders'] = holders_match.group(1)
            metrics['holders_value'] = self._parse_numeric_value(holders_match.group(1), 'holders')
            logger.debug(f"Extracted holders: {metrics['holders']} ({metrics['holders_value']})")

        # Age/open time
        open_match = self.OPEN_PATTERN.search(clean_text)
        if open_match:
            metrics['open'] = open_match.group(1)
            logger.debug(f"Extracted open time: {metrics['open']}")
        else:
            # Try Time as fallback
            time_match = self.TIME_PATTERN.search(clean_text)
            if time_match:
                metrics['open'] = time_match.group(1)
                logger.debug(f"Extracted time as open: {metrics['open']}")

        # Transactions
        txs_match = self.TXS_PATTERN.search(clean_text)
        if txs_match:
            metrics['txs'] = txs_match.group(1)
            metrics['txs_value'] = self._parse_numeric_value(txs_match.group(1), 'transactions')
            logger.debug(f"Extracted transactions: {metrics['txs']} ({metrics['txs_value']})")
        else:
            # Try TXs/Vol format as fallback
            txs_vol_match = self.TXS_VOL_TXS_PATTERN.search(clean_text)
            if txs_vol_match:
                metrics['txs'] = txs_vol_match.group(1)
                metrics['txs_value'] = self._parse_numeric_value(txs_vol_match.group(1), 'transactions')
                logger.debug(f"Extracted transactions from TXs/Vol: {metrics['txs']} ({metrics['txs_value']})")

        # Special handling for Solana Early Trending channel format
        if is_early_trending:
            # These channels often have a specific format with contract address at the end
            # Extract token address if available using enhanced extraction
            channel_info = {'channel_id': self.solearlytrending_channel_id}
            token_address = self.extract_ca_id_only(clean_text, channel_info)
            if token_address:
                metrics['token_address'] = token_address
                logger.debug(f"✅ Enhanced extraction found token address from Early Trending: {token_address}")

            # Early Trending channels often have different formatting for metrics
            # Check for additional patterns specific to these channels
            if 'liquidity_usd' not in metrics:
                # Try alternative liquidity pattern: "LP: $X.XXK"
                alt_liq_match = re.search(r'LP\s*\$([0-9,.]+[KMB]?)', clean_text, re.IGNORECASE)
                if alt_liq_match:
                    metrics['liquidity_usd'] = alt_liq_match.group(1)
                    metrics['liquidity_usd_value'] = self._parse_numeric_value(alt_liq_match.group(1), 'liquidity')
                    logger.debug(f"Extracted alternative liquidity: {metrics['liquidity_usd']} ({metrics['liquidity_usd_value']})")

        # Calculate processing time for performance monitoring
        processing_time = time.time() - start_time
        logger.debug(f"Metrics extraction completed in {processing_time:.3f}s")

        # Log the extracted metrics
        logger.debug(f"Extracted metrics from signal message: {metrics}")
        return metrics

    # Precompile all regex patterns for token address extraction
    # Contract patterns
    CONTRACT_PATTERNS = [
        re.compile(r"Contract:\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"CA:\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Address:\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Token CA\s+([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Token Address:\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Contract Address:\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Token Contract:\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"CA\s*-\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"CA\s*=\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Contract\s*-\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Contract\s*=\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Address\s*-\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Address\s*=\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Token\s*-\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE),
        re.compile(r"Token\s*=\s*([1-9A-HJ-NP-Za-km-z]{32,44})", re.IGNORECASE)
    ]

    # Code block patterns
    CODE_BLOCK_PATTERNS = [
        re.compile(r'`([1-9A-HJ-NP-Za-km-z]{32,44})`'),  # Markdown code block
        re.compile(r'<code>([1-9A-HJ-NP-Za-km-z]{32,44})</code>'),  # HTML code block
        re.compile(r'<pre>([1-9A-HJ-NP-Za-km-z]{32,44})</pre>'),  # HTML pre block
        re.compile(r'```([1-9A-HJ-NP-Za-km-z]{32,44})```'),  # Triple backtick code block
        re.compile(r'```.*?\n([1-9A-HJ-NP-Za-km-z]{32,44})\n.*?```', re.DOTALL),  # Multi-line code block
        re.compile(r'<span.*?>([1-9A-HJ-NP-Za-km-z]{32,44})</span>'),  # HTML span
        re.compile(r'<div.*?>([1-9A-HJ-NP-Za-km-z]{32,44})</div>'),  # HTML div
        re.compile(r'<p.*?>([1-9A-HJ-NP-Za-km-z]{32,44})</p>')  # HTML paragraph
    ]

    # Link patterns
    URL_PATTERN = re.compile(r'https?://[^\s]+')
    ADDRESS_IN_URL_PATTERN = re.compile(r'([1-9A-HJ-NP-Za-km-z]{32,44})')
    MARKDOWN_LINK_PATTERN = re.compile(r'\[.*?\]\(.*?([1-9A-HJ-NP-Za-km-z]{32,44}).*?\)')
    HTML_LINK_PATTERN = re.compile(r'<a href=".*?([1-9A-HJ-NP-Za-km-z]{32,44}).*?">')
    # Removed Jupiter pattern - using only Helius for devnet operations

    # Special patterns for Solana Early Trending channel
    EARLY_TRENDING_PATTERNS = [
        re.compile(r'Contract\s*Address\s*:\s*([1-9A-HJ-NP-Za-km-z]{32,44})', re.IGNORECASE),
        re.compile(r'CA\s*:\s*([1-9A-HJ-NP-Za-km-z]{32,44})', re.IGNORECASE),
        re.compile(r'Token\s*:\s*([1-9A-HJ-NP-Za-km-z]{32,44})', re.IGNORECASE)
    ]

    def extract_token_address(self, message_text: str, channel_info: dict = None) -> Optional[str]:
        """
        Extract token address from message text using optimized pattern matching.

        For Solana Early Trending channel: ONLY accepts Soul Sniper URLs with start=15_ pattern
        For other channels: Uses standard extraction methods

        Args:
            message_text: The message text to extract from
            channel_info: Channel information including channel_id

        Returns:
            The extracted token address or None if none found
        """
        # Start timing for performance tracking
        start_time = time.time()

        if not message_text:
            logger.warning("Empty message text provided to extract_token_address")
            return None

        # CRITICAL: For Solana Early Trending - ONLY accept Soul Sniper URLs
        if channel_info and channel_info.get('channel_id') == self.solearlytrending_channel_id:
            logger.info("SOLANA EARLY TRENDING: Using restricted extraction (Soul Sniper URLs only)")

            # ONLY accept Soul Sniper bot links with start=15_ pattern - NO OTHER FORMATS
            soul_sniper_pattern = r'https://t\.me/soul_sniper_bot\?start=15_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
            match = re.search(soul_sniper_pattern, message_text)
            if match:
                token_address = match.group(1)
                logger.info(f"SOLANA EARLY TRENDING: Extracted token address from Soul Sniper URL: {token_address}")

                # Performance logging
                processing_time = time.time() - start_time
                logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: Solana Early Trending Soul Sniper)")

                return token_address
            else:
                logger.warning(f"SOLANA EARLY TRENDING: No Soul Sniper URL with start=15_ pattern found. Ignoring message.")
                logger.debug(f"SOLANA EARLY TRENDING: Message content: {message_text[:200]}...")

                # Performance logging
                processing_time = time.time() - start_time
                logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: Solana Early Trending - rejected)")

                return None



        # Standard extraction for all other channels - RESTORE ORIGINAL LOGIC
        logger.debug("Using standard token address extraction for non-Early-Trending channel")

        # HIGHEST PRIORITY: Look for addresses ending with "pump" (most reliable for meme coins)
        # SURGICAL FIX: Prioritize pump addresses that appear early in the message (first 500 chars)
        # AND ensure they are complete 44-character addresses
        message_start = message_text[:500]  # Focus on the main content, not holder lists
        pump_addresses_early = re.findall(r"([1-9A-HJ-NP-Za-km-z]{44}pump)", message_start)  # FIXED: Require exactly 44 chars + pump
        if pump_addresses_early:
            token_address = pump_addresses_early[0]
            logger.warning(f"PUMP TOKEN DETECTED (EARLY): Extracted complete token address with 'pump' suffix from message start: {token_address}")

            # Performance logging
            processing_time = time.time() - start_time
            logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: pump suffix early)")

            return token_address

        # Fallback: Look for pump addresses anywhere in the message
        # SURGICAL FIX: Require exactly 44 characters + pump for complete addresses
        pump_addresses = re.findall(r"([1-9A-HJ-NP-Za-km-z]{44}pump)", message_text)  # FIXED: Require exactly 44 chars + pump
        if pump_addresses:
            token_address = pump_addresses[0]
            logger.warning(f"PUMP TOKEN DETECTED: Extracted complete token address with 'pump' suffix: {token_address}")

            # Performance logging
            processing_time = time.time() - start_time
            logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: pump suffix)")

            return token_address

        # If no pump address found, try the general pattern
        # CRITICAL FIX: Prioritize addresses that appear early in the message (first 500 chars)
        early_match = CONTRACT_ADDRESS_REGEX.search(message_start)
        if early_match:
            token_address = early_match.group(1)
            logger.info(f"Extracted token address from message start: {token_address}")

            # Performance logging
            processing_time = time.time() - start_time
            logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: general address pattern early)")

            return token_address

        # Fallback: Look anywhere in the message
        pump_match = CONTRACT_ADDRESS_REGEX.search(message_text)
        if pump_match:
            token_address = pump_match.group(1)
            logger.info(f"Extracted token address: {token_address}")

            # Performance logging
            processing_time = time.time() - start_time
            logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: general address pattern)")

            return token_address

        # Approach 1: Try to extract from "Contract:" or "CA:" line first (most reliable)
        for pattern in self.CONTRACT_PATTERNS:
            contract_match = pattern.search(message_text)
            if contract_match:
                token_address = contract_match.group(1).strip()
                logger.info(f"Extracted token address from Contract/CA line: {token_address}")

                # Performance logging
                processing_time = time.time() - start_time
                logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: contract pattern)")

                return token_address

        # Approach 2: Try to extract from Soul Sniper links directly
        soul_sniper_match = SOUL_SNIPER_REGEX.search(message_text)
        if soul_sniper_match:
            token_address = soul_sniper_match.group(1)
            logger.info(f"Extracted token address from Soul Sniper URL: {token_address}")

            # Performance logging
            processing_time = time.time() - start_time
            logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: Soul Sniper)")

            return token_address

        # Approach 3: Last resort - look for any Solana address pattern in the text
        address_matches = SOLANA_TOKEN_REGEX.findall(message_text)
        if address_matches:
            token_address = address_matches[0]
            logger.info(f"Extracted token address using general Solana address pattern: {token_address}")

            # Performance logging
            processing_time = time.time() - start_time
            logger.debug(f"Token address extraction completed in {processing_time:.3f}s (method: general pattern)")

            return token_address

        # No token address found
        processing_time = time.time() - start_time
        logger.warning(f"Could not extract token address from message: {message_text[:100]}... (processing time: {processing_time:.3f}s)")
        return None

    def _log_sell_notification(self, message_id: str, message: str):
        """Log sell notification to a dedicated file for better tracking."""
        try:
            # Create logs directory if it doesn't exist
            os.makedirs('logs/sell_notifications', exist_ok=True)

            # Write to a dedicated log file
            log_file = f'logs/sell_notifications/sell_{message_id}.txt'
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"SELL NOTIFICATION: {message_id}\n")
                f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                f.write(f"Message: {message}\n")

            logger.info(f"Sell notification logged to {log_file}")
        except Exception as e:
            logger.error(f"Error logging sell notification: {e}")

    def _remove_emojis(self, text):
        """Remove emojis and non-ASCII characters from text to avoid encoding errors in Windows console."""
        if not text:
            return ""

        # Handle non-string inputs
        if not isinstance(text, str):
            try:
                return str(text)
            except Exception:
                return ""

        try:
            # First approach: Use regex to remove common emoji patterns
            import re
            emoji_pattern = re.compile("["
                                    u"\U0001F600-\U0001F64F"  # emoticons
                                    u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                    u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                    u"\U0001F700-\U0001F77F"  # alchemical symbols
                                    u"\U0001F780-\U0001F7FF"  # Geometric Shapes
                                    u"\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
                                    u"\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
                                    u"\U0001FA00-\U0001FA6F"  # Chess Symbols
                                    u"\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
                                    u"\U00002702-\U000027B0"  # Dingbats
                                    u"\U000024C2-\U0001F251"
                                    "]+", flags=re.UNICODE)

            # Replace common emoji symbols with text equivalents
            emoji_map = {
                '🚀': '[ROCKET]',
                '💰': '[MONEY]',
                '💵': '[CASH]',
                '📈': '[CHART]',
                '🔥': '[FIRE]',
                '⚠️': '[WARNING]',
                '✅': '[CHECK]',
                '❌': '[X]',
                '💎': '[DIAMOND]',
                '🌙': '[MOON]',
                '🔴': '[RED]',
                '🟢': '[GREEN]',
                '💹': '[STONKS]',
                '📊': '[GRAPH]',
                '🛑': '[STOP]',
                '⭐': '[STAR]',
                '💯': '[100]',
                '🎯': '[TARGET]',
                '🛢️': '[OIL]',
                '⏳': '[TIMER]',
                '🌳': '[TREE]',
                '🏟️': '[STADIUM]',
                '🚨': '[ALARM]',
                '💊': '[PILL]',
                '🔄': '[REFRESH]',
                '⏱️': '[STOPWATCH]',
                '📝': '[NOTE]',
                '🔍': '[SEARCH]',
                '🔧': '[WRENCH]',
                '🔒': '[LOCK]',
                '🔓': '[UNLOCK]',
                '📉': '[DOWNTREND]',
                '💡': '[IDEA]',
                '⚙️': '[GEAR]',
                '🔔': '[BELL]',
                '🔕': '[MUTED]',
                '📡': '[SIGNAL]',
                '📢': '[ANNOUNCE]',
                '🔁': '[REPEAT]',
                '❗': '[EXCLAMATION]',
                '❓': '[QUESTION]',
                '✔️': '[CHECK]',
                '✓': '[CHECK]',
                '✅': '[CHECK]',
                '❌': '[X]',
                '⛔': '[NO_ENTRY]',
                '🚫': '[PROHIBITED]',
                'ℹ️': '[INFO]'
            }

            # Chinese character replacements
            chinese_map = {
                '市值飙升': '[MARKET_VALUE_SURGE]',
                '进度': '[PROGRESS]',
                '飙升': '[SURGE]',
                '市值': '[MARKET_VALUE]'
            }

            # First replace known emojis with text equivalents
            for emoji, replacement in emoji_map.items():
                if emoji in text:
                    text = text.replace(emoji, replacement)

            # Replace Chinese characters with English equivalents
            for chinese, replacement in chinese_map.items():
                if chinese in text:
                    text = text.replace(chinese, replacement)

            # Then remove any remaining emojis
            text = emoji_pattern.sub(r'', text)

            # CRITICAL FIX: Don't replace emojis with [?] - keep them as-is for Telegram
            # Telegram handles emojis perfectly, so we don't need to remove them
            return text
        except Exception as e:
            logger.error(f"Error removing emojis: {e}")
            # Last resort: try to encode as ASCII
            try:
                return text.encode('ascii', 'ignore').decode('ascii')
            except Exception:
                return "<<text with encoding issues>>"

    # Message queue to prevent rate limiting - ENHANCED WITH ADAPTIVE TIMING
    _message_queue = []
    _message_lock = asyncio.Lock()
    _message_processor_task = None
    _last_message_time = 0
    _message_interval = 0.5  # base seconds between messages
    _message_count_window = []  # Track messages in last 60 seconds
    _activity_levels = {
        'normal': 0.5,    # 0-10 messages/min
        'busy': 1.5,      # 10-20 messages/min
        'overload': 5.0   # 20+ messages/min
    }

    def _get_adaptive_message_interval(self):
        """Calculate adaptive message interval based on recent activity"""
        current_time = time.time()

        # Clean old messages from window (older than 60 seconds)
        self._message_count_window = [
            msg_time for msg_time in self._message_count_window
            if current_time - msg_time < 60
        ]

        # Count messages in last minute
        messages_per_minute = len(self._message_count_window)

        # Determine activity level and return appropriate interval
        if messages_per_minute >= 20:
            return self._activity_levels['overload']  # 5.0s intervals
        elif messages_per_minute >= 10:
            return self._activity_levels['busy']      # 1.5s intervals
        else:
            return self._activity_levels['normal']    # 0.5s intervals

    def _should_batch_message(self, message_type: str) -> bool:
        """Determine if message should be batched during high activity"""
        current_time = time.time()
        messages_per_minute = len([
            msg_time for msg_time in self._message_count_window
            if current_time - msg_time < 60
        ])

        # During overload, only allow critical messages
        if messages_per_minute >= 20:
            critical_types = ['buy_notification', 'sell_notification', 'error']
            return message_type not in critical_types

        return False

    async def _process_message_queue(self):
        """Background task to process messages in the queue with adaptive rate limiting and enhanced reliability."""
        logger.info("Message queue processor started with adaptive rate limiting")

        # Track consecutive errors to implement circuit breaker pattern
        consecutive_errors = 0
        max_consecutive_errors = 5
        circuit_breaker_timeout = 30  # seconds

        while True:
            try:
                # Check if queue is empty
                if not self._message_queue:
                    # No messages to process, sleep briefly and check again
                    await asyncio.sleep(0.1)
                    # Reset error counter when queue is empty
                    consecutive_errors = 0
                    continue

                # Log queue status periodically
                if random.random() < 0.05:  # ~5% chance each iteration
                    queue_size = len(self._message_queue)
                    logger.info(f"Message queue processor active with {queue_size} messages pending")

                # Get a message from the queue with priority handling
                message_data = None
                async with self._message_lock:
                    if not self._message_queue:
                        continue

                    # First check for high priority messages (sell notifications, buy notifications, startup messages)
                    priority_messages = [msg for msg in self._message_queue if msg.get('priority', False)]
                    if priority_messages:
                        # Process the oldest priority message first
                        message_data = priority_messages[0]
                        self._message_queue.remove(message_data)
                        logger.info(f"Processing priority message: {message_data.get('id')}")

                        # Special handling for sell notifications
                        if message_data.get('is_sell', False):
                            try:
                                os.makedirs('logs/sell_notifications', exist_ok=True)
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                with open(f'logs/sell_notifications/sell_{timestamp}.txt', 'w', encoding='utf-8') as f:
                                    f.write(f"--- SELL NOTIFICATION at {datetime.now()} ---\n")
                                    f.write(f"Message ID: {message_data.get('id')}\n")
                                    f.write(f"Message: {message_data.get('message')}\n")
                                logger.info(f"Saved sell notification to logs/sell_notifications/sell_{timestamp}.txt")
                            except Exception as log_error:
                                logger.error(f"Error saving sell notification details: {log_error}")
                    else:
                        # No priority messages, process the next message in the queue
                        message_data = self._message_queue[0]
                        self._message_queue.remove(message_data)  # More reliable than slicing

                # Extract message details
                message_id = message_data['id']
                message = message_data['message']
                is_sell = message_data.get('is_sell', False)
                is_buy = message_data.get('is_buy', False)
                priority = message_data.get('priority', False)

                # Log processing based on message type
                if is_sell:
                    clean_message = self._remove_emojis(message)
                    print(f"\n💰 PROCESSING SELL NOTIFICATION: {clean_message[:100]}...\n")
                    logger.info(f"[{message_id}] Processing sell notification")
                elif is_buy:
                    clean_message = self._remove_emojis(message)
                    print(f"\n🛒 PROCESSING BUY NOTIFICATION: {clean_message[:100]}...\n")
                    logger.info(f"[{message_id}] Processing buy notification")
                elif priority:
                    clean_message = self._remove_emojis(message)
                    print(f"\n🔔 PROCESSING PRIORITY MESSAGE: {clean_message[:100]}...\n")
                    logger.info(f"[{message_id}] Processing priority message")

                # ADAPTIVE RATE LIMITING - Apply dynamic rate limiting based on priority and activity
                current_time = time.time()
                adaptive_interval = self._get_adaptive_message_interval()
                time_since_last = current_time - self._last_message_time

                if time_since_last < adaptive_interval and not priority:
                    # Wait to respect adaptive rate limits, but only for non-priority messages
                    sleep_time = adaptive_interval - time_since_last
                    logger.debug(f"Adaptive rate limiting: sleeping {sleep_time:.2f}s (interval: {adaptive_interval:.2f}s)")
                    await asyncio.sleep(sleep_time)

                # Track message timing for adaptive calculation
                self._message_count_window.append(current_time)

                # Send the message with our robust sending logic
                success = False
                retry_count = 0
                max_retries = 3 if (is_sell or is_buy or priority) else 1  # More retries for important messages

                # Check Telegram connection before sending
                if not self.client or not self.client.is_connected():
                    logger.warning(f"[{message_id}] Telegram client not connected before sending, attempting to reconnect")
                    try:
                        if not self.client:
                            # Try to initialize client
                            await self.connect()
                        else:
                            # Try to reconnect existing client
                            await self.client.connect()

                        if not self.client or not self.client.is_connected():
                            logger.error(f"[{message_id}] Failed to reconnect Telegram client")
                            # Still continue with send attempts
                    except Exception as conn_error:
                        logger.error(f"[{message_id}] Error reconnecting Telegram client: {conn_error}")
                        # Still continue with send attempts

                # Try to send the message with retries
                while not success and retry_count < max_retries:
                    try:
                        # Try to send the message
                        success = await self._send_message_internal(message_id, message, is_sell)

                        if success:
                            # Log success based on message type
                            if is_sell:
                                logger.info(f"[{message_id}] Sell notification sent successfully on attempt {retry_count+1}")
                                print(f"💰 Sell notification sent successfully on attempt {retry_count+1}")
                            elif is_buy:
                                logger.info(f"[{message_id}] Buy notification sent successfully on attempt {retry_count+1}")
                                print(f"🛒 Buy notification sent successfully on attempt {retry_count+1}")
                            elif priority:
                                logger.info(f"[{message_id}] Priority message sent successfully on attempt {retry_count+1}")
                            else:
                                logger.info(f"[{message_id}] Message sent successfully on attempt {retry_count+1}")

                            # Reset consecutive errors on success
                            consecutive_errors = 0
                            break

                        # If failed, retry with exponential backoff
                        retry_count += 1
                        if retry_count < max_retries:
                            backoff_time = 0.5 * (2 ** retry_count)  # Exponential backoff: 1s, 2s, 4s...
                            logger.warning(f"[{message_id}] Send attempt {retry_count} failed, retrying in {backoff_time}s...")
                            if is_sell or is_buy:
                                print(f"⚠️ Notification send attempt {retry_count} failed, retrying in {backoff_time}s...")
                            await asyncio.sleep(backoff_time)
                    except Exception as send_error:
                        logger.error(f"[{message_id}] Error in send attempt {retry_count+1}: {send_error}")
                        retry_count += 1
                        await asyncio.sleep(1)  # Brief pause before retry

                # Update last message time
                self._last_message_time = time.time()

                # Handle failed sends
                if not success:
                    # Increment consecutive error counter
                    consecutive_errors += 1

                    # Log failure based on message type
                    if is_sell:
                        logger.error(f"[{message_id}] Failed to send sell notification after {max_retries} attempts")
                        print(f"❌ Failed to send sell notification after {max_retries} attempts")
                    elif is_buy:
                        logger.error(f"[{message_id}] Failed to send buy notification after {max_retries} attempts")
                        print(f"❌ Failed to send buy notification after {max_retries} attempts")
                    elif priority:
                        logger.error(f"[{message_id}] Failed to send priority message after {max_retries} attempts")
                    else:
                        logger.error(f"[{message_id}] Failed to send message after {max_retries} attempts")

                    # Re-queue important messages that haven't been retried yet
                    if (is_sell or is_buy or priority) and not message_data.get('retried'):
                        logger.warning(f"[{message_id}] Re-queuing important message with higher priority")
                        if is_sell or is_buy:
                            print(f"⚠️ Re-queuing notification with higher priority")

                        # Re-queue with priority flag and retried marker
                        async with self._message_lock:
                            self._message_queue.insert(0, {
                                'id': f"{message_id}_retry",
                                'message': message,  # Keep original message
                                'is_sell': is_sell,
                                'is_buy': is_buy,
                                'priority': True,
                                'retried': True
                            })

                # Circuit breaker pattern - pause processing if too many consecutive errors
                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"Circuit breaker triggered after {consecutive_errors} consecutive errors. Pausing queue processing for {circuit_breaker_timeout} seconds")
                    print(f"⚠️ Message sending circuit breaker triggered. Pausing for {circuit_breaker_timeout} seconds to recover")

                    # Try to reconnect Telegram during the pause
                    try:
                        logger.info("Attempting to reconnect Telegram during circuit breaker pause")
                        if self.client:
                            await self.client.disconnect()
                        await asyncio.sleep(2)
                        await self.connect()
                    except Exception as reconnect_error:
                        logger.error(f"Error reconnecting during circuit breaker pause: {reconnect_error}")

                    # Pause processing
                    await asyncio.sleep(circuit_breaker_timeout)

                    # Reset error counter after pause
                    consecutive_errors = 0
                    logger.info("Circuit breaker reset, resuming message queue processing")

            except Exception as e:
                logger.error(f"Error in message queue processor: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(1)  # Sleep on error to prevent tight loop

    async def send_info_message(self, message: str) -> bool:
        """Queue message to be sent to the info channel with rate limiting and reliability."""
        # Generate a unique ID for this message to track it through the logs
        message_id = f"msg_{int(time.time() * 1000)}_{random.randint(1000, 9999)}"

        # Log sell notifications
        if "Position Closed" in message or "Partial Sell" in message:
            logger.info(f"Received sell notification: {message[:100]}...")

        try:
            # Always log the message regardless of mode
            # Remove emojis from log messages to avoid encoding errors in Windows console
            clean_message = self._remove_emojis(message)

            # Print to console for immediate visibility, with CORRECT notification detection
            # CRITICAL FIX: Properly detect BUY vs SELL notifications
            is_buy_notification = any(keyword in message for keyword in
                                     ['BUY EXECUTED', 'URGENT BUY', 'Buy Executed', 'Trade Executed'])

            # CRITICAL FIX: Only detect SELL if it's actually a sell (not a buy)
            is_sell_notification = (not is_buy_notification and
                                   any(keyword in message.lower() for keyword in
                                      ['sell executed', 'position closed', 'partial sell', 'sell triggered',
                                       'sold', 'selling', 'take profit', 'stop loss', 'exit position', 'exited position',
                                       'position exit', 'profit taken', 'sell notification',
                                       'sell complete', 'sell order', 'sell filled', 'sell successful']))
            is_startup_notification = "Bot Started" in message
            is_balance_notification = "Balance Update" in message

            # Log different types of notifications appropriately
            if is_sell_notification:
                # Print sell notifications to console with high visibility
                print(f"\n💰 SELL NOTIFICATION: {clean_message[:100]}...\n")
                # Log at INFO level to ensure it appears in the CLI
                logger.info(f"[{message_id}] [SELL NOTIFICATION]: {clean_message[:100]}...")
                # Log to dedicated file for better visibility
                self._log_sell_notification(message_id, message)
            elif is_buy_notification:
                print(f"\n🛒 BUY NOTIFICATION: {clean_message[:100]}...\n")
                logger.info(f"[{message_id}] [BUY NOTIFICATION]: {clean_message[:100]}...")
            elif is_startup_notification:
                print(f"\n🚀 STARTUP NOTIFICATION: {clean_message[:100]}...\n")
                logger.info(f"[{message_id}] [STARTUP NOTIFICATION]: {clean_message[:100]}...")
            elif is_balance_notification:
                print(f"\n💰 BALANCE UPDATE: {clean_message[:100]}...\n")
                logger.info(f"[{message_id}] [BALANCE UPDATE]: {clean_message[:100]}...")
            else:
                logger.info(f"[{message_id}] Queuing Telegram message: {clean_message[:100]}...")

            # DIRECT SEND APPROACH: Try to send the message immediately first
            # This bypasses the queue for critical messages and ensures they get sent
            if is_sell_notification or is_buy_notification or is_startup_notification or is_balance_notification:
                try:
                    # Try to send the message directly first
                    direct_send_result = await self._send_message_direct(message_id, message)
                    if direct_send_result:
                        logger.info(f"[{message_id}] Message sent directly successfully")
                        return True
                    else:
                        logger.warning(f"[{message_id}] Direct send failed, falling back to queue")
                        # Fall through to queue-based approach
                except Exception as direct_send_error:
                    logger.warning(f"[{message_id}] Error in direct send: {direct_send_error}, falling back to queue")
                    # Fall through to queue-based approach

            # Start the message processor task if it's not running
            if self._message_processor_task is None or self._message_processor_task.done():
                self._message_processor_task = asyncio.create_task(self._process_message_queue())
                logger.info("Started Telegram message queue processor")

            # Add message to the queue
            async with self._message_lock:
                # Set priority based on message type
                is_priority = is_sell_notification or is_buy_notification or is_startup_notification or is_balance_notification

                # Sell notifications get highest priority in the queue
                if is_sell_notification:
                    # Insert at the beginning of the queue
                    self._message_queue.insert(0, {
                        'id': message_id,
                        'message': message,
                        'is_sell': True,
                        'is_buy': False,
                        'priority': True
                    })
                    logger.info(f"[{message_id}] SELL notification queued with highest priority")
                elif is_priority:
                    # Other important notifications get high priority
                    self._message_queue.insert(0, {
                        'id': message_id,
                        'message': message,
                        'is_sell': False,
                        'is_buy': is_buy_notification,
                        'priority': True
                    })
                    logger.info(f"[{message_id}] Important notification queued with high priority")
                else:
                    # Add to the end of the queue
                    self._message_queue.append({
                        'id': message_id,
                        'message': message,
                        'is_sell': False,
                        'is_buy': False,
                        'priority': False
                    })
                    logger.info(f"[{message_id}] Message queued with normal priority")

            return True
        except Exception as e:
            logger.error(f"[{message_id}] Error queuing message: {e}")
            # Still print notifications to console even if queuing fails
            if is_sell_notification:
                print(f"\n💰 SELL NOTIFICATION (QUEUE FAILED): {clean_message[:100]}...\n")
            elif is_buy_notification:
                print(f"\n🛒 BUY NOTIFICATION (QUEUE FAILED): {clean_message[:100]}...\n")
            return False

    async def check_telegram_connection(self) -> bool:
        """
        Check if the Telegram connection is working properly.

        Returns:
            bool: True if connected, False otherwise
        """
        try:
            if not self.client:
                logger.error("Telegram client is not initialized")
                return False

            if not await self.client.is_user_authorized():
                logger.error("Telegram client is not authorized")
                return False

            # Try to get info about the bot info channel
            info_channel_id_str = self.telegram_settings.get('bot_info_channel_id')
            if not info_channel_id_str:
                logger.error("bot_info_channel_id not set in config")
                return False

            try:
                info_channel_id = int(info_channel_id_str)
                entity = await self.client.get_entity(info_channel_id)
                logger.info(f"Successfully resolved info channel: {entity.title if hasattr(entity, 'title') else 'Unknown'}")
                return True
            except Exception as e:
                logger.error(f"Failed to resolve info channel: {e}")
                return False

        except Exception as e:
            logger.error(f"Error checking Telegram connection: {e}")
            return False

    async def _send_message_direct(self, message_id: str, message: str) -> bool:
        """
        Simplified direct message sending method with improved reliability.
        This is a streamlined version that focuses on getting the message sent quickly.

        Args:
            message_id: Unique identifier for this message
            message: The message content to send

        Returns:
            bool: True if message was sent successfully, False otherwise
        """
        # Add timestamp and special formatting to make messages more visible
        timestamp = datetime.now().strftime("%H:%M:%S")

        # CRITICAL FIX: Check if message is a rug protection notification first
        is_rug_protection = "🛡️ [RUG PROTECTION" in message or "rug protection" in message.lower() or "token blocked by rug protection" in message.lower()

        # Check if message is a buy notification (but exclude rug protection messages)
        is_buy = not is_rug_protection and ("buy executed" in message.lower() or "buying" in message.lower() or "trade executed" in message.lower() or "urgent buy executed" in message.lower())

        # Check if message is a sell notification
        is_sell = "sell" in message.lower() or "sold" in message.lower() or "profit" in message.lower()

        # Check if message already has a header
        has_sell_header = "🟣 [SELL EXECUTED]" in message
        has_buy_header = "🔴 URGENT BUY NOTIFICATION" in message
        has_rug_header = "🛡️ [RUG PROTECTION" in message

        # Add appropriate prefix based on message type, but only if it doesn't already have a header
        if is_buy and not has_buy_header and not has_rug_header:
            message = f"🔴 URGENT BUY NOTIFICATION [{timestamp}]\n\n{message}"
        elif is_sell and not has_sell_header:
            message = f"🟣 [SELL EXECUTED] — {self.run_mode}\n\n{message}"
        elif "[BELL]" in message:
            # Replace [BELL] with more visible format
            message = message.replace("[BELL] New Signal Detected:", "🚨 NEW SIGNAL DETECTED")
            message = f"🚨 URGENT SIGNAL NOTIFICATION [{timestamp}]\n\n{message}\n\n⚠️ CHECK YOUR TELEGRAM BOT INFO CHANNEL FOR DETAILS ⚠️"
        elif not has_buy_header and not has_sell_header:
            message = f"⚠️ IMPORTANT BOT MESSAGE [{timestamp}]\n\n{message}"
        try:
            # Check if client is initialized and connected
            if not self.client:
                logger.warning(f"[{message_id}] Cannot send message directly - Telegram client not initialized")
                return False

            if not self.client.is_connected():
                logger.warning(f"[{message_id}] Telegram client not connected for direct send")
                try:
                    # Quick reconnect attempt
                    await self.client.connect()
                    logger.info(f"[{message_id}] Reconnected Telegram client for direct send")
                except Exception as conn_error:
                    logger.warning(f"[{message_id}] Reconnection failed for direct send: {conn_error}")
                    return False

            # Get info channel ID from settings
            info_channel_id_str = self.telegram_settings.get('bot_info_channel_id')
            if not info_channel_id_str:
                logger.warning(f"[{message_id}] Cannot send message directly - 'bot_info_channel_id' not set in config")
                return False

            # Ensure the ID is an integer
            info_channel_id_str = str(info_channel_id_str).strip('"\'')
            info_channel_id = int(info_channel_id_str)

            # Try to resolve the entity first (this helps with caching)
            try:
                logger.info(f"[{message_id}] Resolving info channel entity for ID: {info_channel_id}")
                entity = await self.client.get_entity(info_channel_id)
                logger.info(f"[{message_id}] Successfully resolved info channel entity: {getattr(entity, 'title', str(info_channel_id))}")

                # Use the resolved entity to send the message
                async with asyncio.timeout(5):  # 5 second timeout
                    await self.client.send_message(
                        entity,
                        message,
                        parse_mode='html',
                        schedule=None  # Don't schedule, send immediately
                    )
                logger.info(f"[{message_id}] Message sent directly successfully using entity")
                return True
            except Exception as entity_error:
                logger.warning(f"[{message_id}] Entity resolution or send failed: {entity_error}, trying direct ID approach")

                # Fall back to direct ID approach if entity resolution fails
                try:
                    logger.info(f"[{message_id}] Sending message directly to channel ID: {info_channel_id}")
                    # Use a short timeout for direct send
                    async with asyncio.timeout(5):  # 5 second timeout
                        await self.client.send_message(
                            info_channel_id,
                            message,
                            parse_mode='html',
                            schedule=None  # Don't schedule, send immediately
                        )
                    logger.info(f"[{message_id}] Message sent directly successfully using ID")
                    return True
                except Exception as direct_error:
                    logger.warning(f"[{message_id}] Direct send failed: {direct_error}")

                    # Try one more time with a different approach
                    try:
                        logger.info(f"[{message_id}] Trying final approach with string ID")
                        # Try with string ID as a last resort
                        async with asyncio.timeout(5):  # 5 second timeout
                            await self.client.send_message(
                                str(info_channel_id),
                                message,
                                parse_mode='html',
                                schedule=None  # Don't schedule, send immediately
                            )
                        logger.info(f"[{message_id}] Message sent successfully with string ID")
                        return True
                    except Exception as final_error:
                        logger.error(f"[{message_id}] All direct send approaches failed: {final_error}")
                        return False

        except Exception as e:
            logger.error(f"[{message_id}] Error in direct send: {e}")
            return False

    async def _send_message_internal(self, message_id: str, message: str, is_sell_notification: bool) -> bool:
        """
        Internal method to send a message to the info channel with ultra-robust error handling.

        This optimized implementation includes:
        - Special high-priority handling for sell notifications
        - Multiple fallback mechanisms for critical messages
        - Detailed logging for troubleshooting
        - Automatic retry with exponential backoff

        Args:
            message_id: Unique identifier for this message
            message: The message content to send
            is_sell_notification: Whether this is a sell notification (gets special handling)

        Returns:
            bool: True if message was sent successfully, False otherwise
        """
        # For sell notifications, we'll use a more aggressive approach with multiple retries

        # Check Telegram connection first for sell notifications
        if is_sell_notification:
            connection_ok = await self.check_telegram_connection()
            logger.info(f"Telegram connection check result: {connection_ok}")
            if not connection_ok:
                logger.error("Telegram connection check failed before sending sell notification")
                # Still continue to try sending the message

        try:
            # Always print sell notifications to console regardless of Telegram status
            if is_sell_notification:
                clean_message = self._remove_emojis(message)
                print(f"\n🟣 SENDING SELL NOTIFICATION TO TELEGRAM: {clean_message[:100]}...\n")

                # Log to dedicated file with a unique ID for this send attempt
                send_id = f"{message_id}_send_{int(time.time())}"
                self._log_sell_notification(send_id, message)

                # Add extra logging for sell notifications
                logger.info(f"Attempting to send sell notification to Telegram: {message_id}")

                # For sell notifications, also log to a special file that's easy to find
                try:
                    os.makedirs('logs/sell_notifications', exist_ok=True)
                    with open(f'logs/sell_notifications/sell_{send_id}.txt', 'w', encoding='utf-8') as f:
                        f.write(f"SELL NOTIFICATION: {message_id}\n")
                        f.write(f"Timestamp: {datetime.now().isoformat()}\n")
                        f.write(f"Message: {message}\n")
                except Exception as log_error:
                    logger.warning(f"Failed to write sell notification to special log: {log_error}")

            # Ensure client is initialized
            if not self.client:
                logger.warning(f"[{message_id}] Cannot send message - Telegram client not initialized.")

                # For sell notifications, make an emergency attempt to initialize the client
                if is_sell_notification:
                    try:
                        logger.warning(f"[{message_id}] Emergency client initialization for sell notification")
                        # Try to initialize the client
                        await self.connect()  # Use the existing connect method instead of _init_telegram_client
                        if not self.client:
                            logger.error(f"[{message_id}] Emergency client initialization failed")
                            return False
                    except Exception as init_error:
                        logger.error(f"[{message_id}] Emergency client initialization failed: {init_error}")
                        return False
                else:
                    return False

            # Get info channel ID from settings
            info_channel_id_str = self.telegram_settings.get('bot_info_channel_id')
            if not info_channel_id_str:
                logger.warning(f"[{message_id}] Cannot send message - 'bot_info_channel_id' not set in config.")
                return False

            # Try to use the direct send method first, which has multiple fallback mechanisms
            try:
                direct_result = await self._send_message_direct(message_id, message)
                if direct_result:
                    logger.info(f"[{message_id}] Message sent successfully via direct send method")
                    return True
                else:
                    logger.warning(f"[{message_id}] Direct send method failed, falling back to legacy approach")
            except Exception as direct_error:
                logger.warning(f"[{message_id}] Error in direct send method: {direct_error}, falling back to legacy approach")

            # Ensure the ID is an integer
            info_channel_id_str = str(info_channel_id_str).strip('"\'')
            info_channel_id = int(info_channel_id_str)

            if is_sell_notification:
                logger.info(f"[{message_id}] Processing SELL NOTIFICATION with high priority...")

            # First attempt: Check connection and reconnect if needed
            if not self.client.is_connected():
                logger.warning(f"[{message_id}] Telegram client not connected. Attempting to reconnect...")
                try:
                    await self.client.connect()
                    logger.info(f"[{message_id}] Successfully reconnected to Telegram")
                except Exception as conn_error:
                    logger.warning(f"[{message_id}] Reconnection attempt failed: {conn_error}")
                    # Continue anyway - we'll try to send the message regardless

            # Try to resolve the info channel entity if not already cached
            if not hasattr(self, 'info_channel') or not self.info_channel:
                try:
                    logger.info(f"[{message_id}] Resolving info channel entity for ID: {info_channel_id}")
                    self.info_channel = await self.client.get_entity(info_channel_id)
                    logger.info(f"[{message_id}] Successfully resolved info channel entity: {getattr(self.info_channel, 'title', str(info_channel_id))}")
                except Exception as e:
                    logger.warning(f"[{message_id}] Failed to resolve info channel entity: {e}")
                    # Continue with direct ID approach

            # Use cached entity if available to avoid entity resolution delay
            if hasattr(self, 'info_channel') and self.info_channel:
                try:
                    # Use a strict timeout for the send operation
                    async with asyncio.timeout(5):  # 5 second timeout
                        await self.client.send_message(
                            self.info_channel,
                            message,
                            parse_mode='html',
                            schedule=None  # Don't schedule, send immediately
                        )
                    logger.info(f"[{message_id}] Message sent successfully using cached entity")
                    return True
                except (asyncio.TimeoutError, Exception) as e:
                    logger.warning(f"[{message_id}] Failed to send with cached entity: {e}")
                    # Fall through to direct ID approach

            # Direct ID approach - more reliable than entity resolution
            try:
                logger.info(f"[{message_id}] Attempting to send message using direct channel ID: {info_channel_id}")
                # Use a strict timeout for the send operation
                async with asyncio.timeout(5):  # 5 second timeout
                    await self.client.send_message(
                        info_channel_id,
                        message,
                        parse_mode='html',
                        schedule=None  # Don't schedule, send immediately
                    )
                logger.info(f"[{message_id}] Message sent successfully using direct ID")
                return True
            except (asyncio.TimeoutError, Exception) as direct_error:
                logger.error(f"[{message_id}] Direct ID send failed: {direct_error}")

                # First retry with reconnection
                try:
                    # Try to reconnect
                    logger.info(f"[{message_id}] Attempting reconnection for retry...")
                    await self.client.disconnect()  # Ensure disconnected first
                    await asyncio.sleep(0.5)  # Brief pause
                    await self.client.connect()
                    logger.info(f"[{message_id}] Reconnected to Telegram for retry")

                    # Try direct send again with timeout
                    async with asyncio.timeout(5):  # 5 second timeout
                        await self.client.send_message(
                            info_channel_id,
                            message,
                            parse_mode='html',
                            schedule=None
                        )
                    logger.info(f"[{message_id}] Message sent successfully after reconnection")
                    return True
                except (asyncio.TimeoutError, Exception) as retry_error:
                    logger.error(f"[{message_id}] Retry after reconnect failed: {retry_error}")

                    # For sell notifications, make an extra attempt with a fresh connection
                    if is_sell_notification:
                        try:
                            logger.info(f"[{message_id}] Making final attempt for SELL notification...")
                            # Try to disconnect and reconnect completely
                            await self.client.disconnect()
                            await asyncio.sleep(1)  # Brief pause
                            await self.client.connect()

                            # Try to resolve the entity again
                            self.info_channel = await self.client.get_entity(info_channel_id)

                            # Final attempt with timeout
                            async with asyncio.timeout(5):  # 5 second timeout
                                await self.client.send_message(
                                    info_channel_id,  # Use direct ID for final attempt
                                    message,
                                    parse_mode='html',
                                    schedule=None
                                )
                            logger.info(f"[{message_id}] SELL notification sent successfully after full reconnection")
                            return True
                        except (asyncio.TimeoutError, Exception) as final_error:
                            logger.error(f"[{message_id}] Final attempt for SELL notification failed: {final_error}")

                            # For sell notifications, make one last desperate attempt with a different approach
                            if is_sell_notification:
                                try:
                                    logger.warning(f"[{message_id}] Making emergency direct attempt for SELL notification")
                                    # Try a completely different approach as last resort
                                    if hasattr(self, 'client') and self.client:
                                        # Get all dialogs and find the info channel
                                        dialogs = await self.client.get_dialogs()
                                        for dialog in dialogs:
                                            if hasattr(dialog, 'id') and str(dialog.id) == str(info_channel_id):
                                                logger.info(f"[{message_id}] Found info channel in dialogs, sending emergency message")
                                                await self.client.send_message(dialog, f"🟣 EMERGENCY SELL NOTIFICATION: {message}")
                                                return True

                                        # If we couldn't find the exact channel, try to send to any channel as a last resort
                                        if dialogs:
                                            # Find the first channel that looks like a bot channel
                                            for dialog in dialogs:
                                                if hasattr(dialog, 'title') and dialog.title and ('bot' in dialog.title.lower() or 'info' in dialog.title.lower()):
                                                    logger.warning(f"[{message_id}] Last resort: Sending to alternative channel: {dialog.title}")
                                                    await self.client.send_message(dialog, f"🟣 CRITICAL EMERGENCY SELL NOTIFICATION (CHANNEL FALLBACK): {message}")
                                                    return True
                                except Exception as emergency_error:
                                    logger.error(f"[{message_id}] Emergency sell notification attempt failed: {emergency_error}")

                    return False

        except ValueError:
            logger.error(f"[{message_id}] Invalid bot_info_channel_id in config: '{info_channel_id_str}'. Must be an integer.")
            return False
        except Exception as e:
            logger.error(f"[{message_id}] Error in send_message_internal: {e}")
            return False
        finally:
            # Log performance metrics for sell notifications
            if is_sell_notification:
                # Use a fixed value since we don't have a start_time variable
                logger.info(f"[{message_id}] Sell notification processing completed")

    async def process_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process incoming message for signals"""
        try:
            # Extract signal using the refactored method
            signal_details = await self._extract_signal(message) # Renamed to avoid conflict
            if not signal_details:
                return None

            # Check cooldowns (using token_address from signal_details)
            # The _check_cooldowns method might need adjustment if it expects 'token' key
            # For now, assuming it can work with 'token_address' or we'll adjust it later.
            # Let's create a temporary dict for _check_cooldowns if it strictly expects 'token'
            temp_signal_for_cooldown = {'token': signal_details['token_address'], 'channel': signal_details['channel_id']}
            if not await self._check_cooldowns(temp_signal_for_cooldown): # Pass only relevant parts for cooldown
                logger.info(f"Signal for {signal_details['token_address']} on cooldown.")
                return None

            # Validate signal (this also might need adjustment if it relied on confidence)
            # For now, let's assume _validate_signal will be adapted or simplified.
            # We pass the full signal_details here as validation might use various parts.
            if not await self._validate_signal(signal_details):
                logger.warning(f"Signal validation failed for {signal_details['token_address']}.")
                return None

            # Add to queue (the full signal_details dictionary)
            await self.signal_queue.put(signal_details)
            logger.info(f"Signal for {signal_details['token_address']} queued. Queue size: {self.signal_queue.qsize()}")


            # Store in recent signals list (keep most recent 10)
            token_address = signal_details['token_address']

            async with self.signal_lock:
                # Add to the beginning of the list
                self.recent_signals.insert(0, token_address)
                # Keep only the most recent 10 signals
                self.recent_signals = self.recent_signals[:10]

                # Store detailed signal data (without confidence from this stage)
                self.recent_signal_data[token_address] = {
                    'timestamp': datetime.now(), # Or use signal_details['timestamp']
                    'source_type': signal_details['source_type'],
                    'raw_message': signal_details.get('raw_message', ''),
                    'channel_id': signal_details.get('channel_id', 'Unknown'),
                    'text_metrics': signal_details.get('text_metrics', {})
                    # 'confidence' is NOT stored here anymore
                }
                # self.token_confidence.pop(token_address, None) # Remove any old confidence score

            logger.info(f"Added signal to recent signals: {token_address}")
            logger.info(f"Recent signals count: {len(self.recent_signals)}")
            logger.info(f"Recent signals: {self.recent_signals}")

            # Send notification (without confidence)
            title = "🔔 New Raw Signal Detected:"

            elif signal_details['source_type'] == 'early':
                title = "📈 SolEarlyTrending Raw Signal:"

            # Create a summary of text_metrics for the notification
            metrics_summary = ", ".join(f"{k}: {v}" for k, v in signal_details.get('text_metrics', {}).items() if v)
            if len(metrics_summary) > 100: # Keep it concise
                metrics_summary = metrics_summary[:97] + "..."

            signal_msg = (
                f"{title}\\n"
                f"Token: <code>{signal_details['token_address']}</code>\\n"
                f"Source: {signal_details['source_type']} (Channel: {signal_details.get('channel_id', 'N/A')})\\n"
                f"Timestamp: {datetime.fromtimestamp(signal_details['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            if metrics_summary:
                signal_msg += f"\\nRaw Metrics: {metrics_summary}"
            else:
                signal_msg += "\\n(No raw metrics extracted)"


            await self.send_info_message(signal_msg)
            return signal_details # Return the processed signal_details

        except Exception as e:
            logger.error(f"Error in process_message for message '{message.get('text', '')[:100]}...': {e}", exc_info=True)
            # log_error("signal_processing", str(e), message.get('text', '')) # Already done by logger
            return None

    async def process_price_update(self, token_address: str, price: float, volume: float) -> Dict[str, Any]:
        """Process price update and generate trading signal"""
        try:
            # Update price history
            if token_address not in self.price_history:
                self.price_history[token_address] = []
                self.volume_history[token_address] = []

            self.price_history[token_address].append(price)
            self.volume_history[token_address].append(volume)

            # Keep only last N points
            if len(self.price_history[token_address]) > self.max_history:
                self.price_history[token_address] = self.price_history[token_address][-self.max_history:]
                self.volume_history[token_address] = self.volume_history[token_address][-self.max_history:]

            # Calculate indicators
            indicators = self._calculate_indicators(token_address)

            # Generate trading signal
            signal = self._generate_trading_signal(token_address, price, volume, indicators)

            # Send Telegram message for significant signals
            if signal["action"] in ["buy", "sell"]:
                message = (
                    f"🔔 <b>{signal['type'].upper()} Signal</b>\n\n"
                    f"Token: <code>{token_address}</code>\n"
                    f"Price: {format_sol_amount(price)}\n"
                    f"Volume: {format_usd_amount(volume)}\n"
                    f"Confidence: {format_percentage(signal['confidence'] * 100)}\n"
                )
                # Technical indicators removed - using fundamental analysis only

                await self.send_info_message(message)

            # Log signal
            log_signal(
                signal["type"],
                token_address,
                signal["confidence"],
                price,
                volume
            )

            # Log historical signal
            log_historical_signal(token_address, signal["type"], price, volume)

            return signal

        except Exception as e:
            log_error("price_processing", str(e), token_address)
            return {"action": "error", "error": str(e)}

    def _calculate_indicators(self, token_address: str) -> Dict[str, float]:
        """Calculate technical indicators"""
        # Technical indicators removed - using fundamental analysis only
        _ = token_address  # Unused parameter
        return {}

    def _generate_trading_signal(self, token_address: str, price: float, volume: float, indicators: Dict[str, float]) -> Dict[str, Any]:
        """Generate trading signal based on indicators"""
        # Initialize signal with default values
        _ = indicators  # Unused parameter
        signal = {
            "type": "neutral",
            "confidence": 0.0,
            "action": "hold",
            "token": token_address,
            "price": price,
            "volume": volume
        }

        # Technical indicators removed - using fundamental analysis only

        return signal

    def _extract_token_addresses(self, text: str, chat_id: int = None) -> List[str]:
        """Extract token addresses from message text using simplified regex patterns from Test directory."""
        addresses = []
        pump_addresses = []  # Special list for addresses ending with "pump"

        # Check if this is a Solana Early Trending message by channel ID
        is_early_trending = (chat_id == self.solearlytrending_channel_id)  # -1002093384030
        logger.info(f"Is Solana Early Trending message: {is_early_trending} (chat_id: {chat_id})")

        # CRITICAL: For Solana Early Trending, ONLY accept soul_sniper_bot links with 15_ prefix
        if is_early_trending:
            # ONLY accept Soul Sniper bot links with start=15_ pattern - NO OTHER FORMATS
            soul_sniper_pattern = r'https://t\.me/soul_sniper_bot\?start=15_([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)'
            match = re.search(soul_sniper_pattern, text)
            if match:
                ca_id = match.group(1)
                logger.info(f"SOLANA EARLY TRENDING: Extracted CA from soul_sniper_bot link: {ca_id}")
                return [ca_id]
            else:
                logger.warning(f"SOLANA EARLY TRENDING: No soul_sniper_bot link with 15_ prefix found. Skipping message.")
                logger.debug(f"SOLANA EARLY TRENDING: Message content: {text[:200]}...")
                return []

        # HIGHEST PRIORITY: Check for Soul Sniper links (most reliable source for token addresses)
        soul_matches = SOUL_SNIPER_REGEX.findall(text)
        if soul_matches:
            for addr in soul_matches:
                if addr.endswith("pump"):
                    # If the address ends with "pump", it's highest priority
                    logger.info(f"Found contract address with 'pump' suffix in Soul Sniper link: {addr}")
                    return [addr]  # Return immediately
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address from Soul Sniper link: {addr}")
            # If we found any Soul Sniper addresses, return them immediately
            if addresses:
                logger.info(f"Using Soul Sniper address as highest priority: {addresses[0]}")
                return addresses

        # HIGH PRIORITY: Check for CA: format
        ca_matches = CA_LINE_REGEX.findall(text)
        if ca_matches:
            for addr in ca_matches:
                if addr.endswith("pump"):
                    # If the address ends with "pump", it's highest priority
                    logger.info(f"Found contract address with 'pump' suffix in CA line: {addr}")
                    return [addr]  # Return immediately
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address from CA line: {addr}")
            # If we found any CA addresses, return them immediately
            if addresses:
                logger.info(f"Using CA line address as high priority: {addresses[0]}")
                return addresses

        # Try each of the token address patterns from Test directory
        for pattern in TOKEN_ADDRESS_PATTERNS:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                for addr in matches:
                    # If the address ends with "pump", add it to pump_addresses
                    if addr.endswith("pump"):
                        if addr not in pump_addresses:
                            pump_addresses.append(addr)
                            logger.info(f"Found contract address with 'pump' suffix: {addr}")
                    # Otherwise add it to regular addresses
                    elif addr not in addresses:
                        addresses.append(addr)
                        logger.info(f"Found contract address: {addr}")

        # If we found any pump addresses, return them immediately (highest priority)
        if pump_addresses:
            logger.info(f"Using 'pump' address as highest priority: {pump_addresses[0]}")
            return pump_addresses

        # Extract from Telegram bot links
        telegram_bot_matches = TELEGRAM_BOT_START_REGEX.findall(text)
        if telegram_bot_matches:
            for addr in telegram_bot_matches:
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address from Telegram bot link: {addr}")

        # Extract from DexScreener URLs
        dexscreener_matches = DEXSCREENER_URL_REGEX.findall(text)
        if dexscreener_matches:
            for addr in dexscreener_matches:
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address from DexScreener URL: {addr}")

        # Extract from Raydium URLs
        raydium_matches = RAYDIUM_URL_REGEX.findall(text)
        if raydium_matches:
            for addr in raydium_matches:
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address from Raydium URL: {addr}")

        # Extract from Birdeye URLs
        birdeye_matches = BIRDEYE_URL_REGEX.findall(text)
        if birdeye_matches:
            for addr in birdeye_matches:
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address from Birdeye URL: {addr}")

        # Extract from GeckoTerminal URLs (DISABLED - URLs contain pool addresses, not token addresses)
        # GeckoTerminal URLs like https://geckoterminal.com/solana/pools/XXXXX contain POOL addresses, not TOKEN addresses
        # This causes the bot to analyze the wrong address. Disabling this extraction method.
        # geckoterminal_matches = GECKOTERMINAL_REGEX.findall(text)
        # if geckoterminal_matches:
        #     for addr in geckoterminal_matches:
        #         if addr not in addresses:
        #             addresses.append(addr)
        #             logger.warning(f"DISABLED: GeckoTerminal URL contains pool address, not token address: {addr}")

        # Direct regex match for Solana addresses (lowest priority)
        # Only use this if we haven't found any addresses yet
        if not addresses:
            sol_matches = SOLANA_TOKEN_REGEX.findall(text)
            for addr in sol_matches:
                if addr not in addresses:
                    addresses.append(addr)
                    logger.info(f"Extracted contract address using general Solana address pattern: {addr}")

        # Remove duplicates while preserving order (first found has priority)
        unique_addresses = []
        for addr in addresses:
            if addr not in unique_addresses:
                unique_addresses.append(addr)

        # Log the results
        if unique_addresses:
            logger.info(f"Found {len(unique_addresses)} unique token addresses")
            for addr in unique_addresses[:3]:  # Log first 3 addresses to avoid cluttering logs
                logger.info(f"Token address: {addr}")
            if len(unique_addresses) > 3:
                logger.info(f"... and {len(unique_addresses) - 3} more addresses")
        else:
            logger.info("No token addresses found in message")

        return unique_addresses

    async def _check_cooldowns(self, signal: Dict[str, Any]) -> bool:
        """
        Check if a signal is on cooldown.

        Args:
            signal: Signal dictionary

        Returns:
            True if the signal is not on cooldown, False otherwise
        """
        # Get token from either token_address or token field
        token_key = None
        if 'token_address' in signal and signal['token_address']:
            token_key = signal['token_address']
            # Add token field for backward compatibility
            signal['token'] = token_key
        elif 'token' in signal and signal['token']:
            token_key = signal['token']
        else:
            logger.warning(f"No token or token_address in signal: {signal}")
            # Add empty token field to prevent future errors
            signal['token'] = ""
            return True  # Allow processing if no token key found

        # Get channel from either channel_id or channel field
        channel_id = signal.get('channel', signal.get('channel_id', ''))
        if channel_id:
            # Ensure channel field exists for compatibility
            signal['channel'] = channel_id

        # Skip cooldown check for high-priority channels
        # (This is now handled in the channel cooldown section below)

        current_time = time.time()

        # Check token cooldown
        if token_key and token_key in self.cooldowns:
            last_time = self.cooldowns[token_key]
            if current_time - last_time < self.cooldown_period:
                remaining = int(self.cooldown_period - (current_time - last_time))
                logger.info(f"Token {token_key} is on cooldown for {remaining} more seconds")
                return False

        # Check channel cooldown
        if channel_id and channel_id in self.channel_cooldowns:
            last_time = self.channel_cooldowns[channel_id]
            if current_time - last_time < self.channel_cooldown_period:
                remaining = int(self.channel_cooldown_period - (current_time - last_time))
                logger.info(f"Channel {channel_id} is on cooldown for {remaining} more seconds")
                return False

        # Update cooldowns
        if token_key:
            self.cooldowns[token_key] = current_time
        if channel_id:
            self.channel_cooldowns[channel_id] = current_time

        return True

    async def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """
        Validate a signal.

        Args:
            signal: Signal dictionary

        Returns:
            True if the signal is valid, False otherwise
        """
        # Check if token address is valid
        token = signal.get('token', '')
        if not token or len(token) < 32 or len(token) > 44:
            logger.warning(f"Invalid token address: {token}")
            return False

        # Check if confidence is valid
        confidence = signal.get('confidence', 0.0)
        if confidence < 0.0 or confidence > 1.0:
            logger.warning(f"Invalid confidence: {confidence}")
            return False

        return True

    async def _extract_signal(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract signal from message, focusing on token, source, and raw metrics."""
        text = message.get('text', '')
        if not text: # Added check for empty text
            return None
        chat_id = message.get('chat_id')
        is_gmgn_channel = False

        # Attempt to get a resolved channel name if possible, otherwise use chat_id
        # This might require resolving chat_id to a channel entity name if not already done
        channel_identifier = message.get('channel_identifier', str(chat_id)) # Use provided channel_identifier or fallback to chat_id

        # Channel ID to name mapping
        channel_names = {
            -1002093384030: "Solana Early Trending",
            -1002187518673: "DONALD CALLS",
            -1002017857449: "PEPE CALLS",
            -1002177594166: "OxyZen Calls",
            -1002017173747: "SOL HIGH VOLUME ALERT | Bordga",
            -1002428819353: "Solbix Community Calls",
            -1001509251052: "Solana Gems Radar"
        }

        # Update channel_identifier based on chat_id
        if chat_id in channel_names:
            channel_identifier = channel_names[chat_id]
            logger.info(f"Setting channel_identifier to '{channel_identifier}' based on chat_id {chat_id}")



        # Use the comprehensive _extract_token_addresses method
        # Assuming it's designed to return a list, and we take the first/best one
        # Or, if extract_token_address is preferred and returns a single Optional[str]

        # First try to extract token addresses using the comprehensive method
        token_addresses = self._extract_token_addresses(text, chat_id)

        # If we found addresses, use the first one (highest priority)
        if token_addresses:
            token_address = token_addresses[0]
            # Special handling for pump tokens - log it clearly
            if token_address.endswith("pump"):
                logger.warning(f"PUMP TOKEN DETECTED in signal: {token_address}")
        else:
            # Fallback to the enhanced address extractor with channel info
            channel_info = {'channel_id': chat_id}  # Use actual chat_id, not channel_identifier string
            token_address = self.extract_ca_id_only(text, channel_info)
            if not token_address:
                # Final fallback to original method with channel info
                token_address = self.extract_token_address(text, channel_info)

        if not token_address:
            logger.debug(f"No token address found in message from {channel_identifier}: {text[:100]}...")
            return None

        # Now, extract all available text-based metrics
        text_metrics = self._extract_metrics(text) # This should return a dict

        # Determine source type (can be refined)
        source_type = "unknown_telegram" # Default

        # Determine source type based on channel and content
        # Use channel_identifier as the source for better notification display
        if chat_id == -1002428819353:
            # Solbix Community Calls channel - CA is directly available
            source_type = 'Solbix Community Calls'
            logger.info(f"Detected Solbix Community Calls signal for token: {token_address}")
            logger.info("CA extraction: Direct from message text (no hyperlink parsing needed)")
        elif chat_id in channel_names:
            # Use the proper channel name as source
            source_type = channel_names[chat_id]
            logger.info(f"Detected signal from {source_type} for token: {token_address}")

        elif re.search(self.regex_patterns.get('early', '(?!)'), text):
            source_type = 'Solana Early Trending'
        elif re.search(self.regex_patterns.get('soul_sniper', '(?!)'), text):
            source_type = 'soul_sniper'
        elif re.search(self.regex_patterns.get('generic_link', '(?!)'), text):
            source_type = 'generic_link'
        elif token_address: # If we have a token address, it's at least a basic signal
             source_type = channel_identifier if channel_identifier != str(chat_id) else 'telegram_basic'


        # Ensure text_metrics is not None before getting its length
        metrics_count = len(text_metrics) if text_metrics is not None else 0
        logger.info(f"Extracted signal: Token={token_address}, Source={source_type}, Metrics Count={metrics_count}")

        # Determine the proper source name for the signal
        source_name = channel_identifier or source_type

        return {
            'token_address': token_address,
            'token': token_address,         # Add token field for backward compatibility
            'source_type': source_type,
            'source': source_name,          # Add source field for bot_controller compatibility
            'raw_message': text,
            'channel_id': channel_identifier, # Store channel_id or resolved name
            'channel': channel_identifier,   # Add channel field for backward compatibility
            'source_channel_id': chat_id,    # Add source_channel_id for channel detection
            'source_channel': channel_identifier or '', # Ensure source_channel is set
            'text_metrics': text_metrics or {},  # Ensure text_metrics is at least an empty dict
            'extracted_metrics': text_metrics or {},  # Add extracted_metrics for compatibility
            'signal_message': text,          # Add signal_message for compatibility
            'timestamp': time.time(),        # Add current timestamp

            # NO 'confidence' field here
        }

    # Simulation signal generation removed - real signals only

    async def _check_cooldowns(self, signal: Dict[str, Any]) -> bool:
        """Check cooldowns for token and channel"""
        now = datetime.now().timestamp()

        # Check for either token_address or token key in the signal
        token_key = None
        if 'token_address' in signal and signal['token_address']:
            token_key = signal['token_address']
            # Add token field for backward compatibility
            signal['token'] = token_key
        elif 'token' in signal and signal['token']:
            token_key = signal['token']
        else:
            logger.warning(f"No token or token_address in signal: {signal}")
            # Add empty token field to prevent future errors
            signal['token'] = ""
            return True  # Allow processing if no token key found

        # Check token cooldown if we have a token key
        if token_key in self.cooldowns:
            # Use special shorter cooldown for pump tokens
            if token_key.endswith("pump"):
                cooldown_to_use = self.pump_token_cooldown_period
                logger.info(f"Using shorter cooldown ({cooldown_to_use}s) for pump token: {token_key}")
            else:
                cooldown_to_use = self.cooldown_period

            if now - self.cooldowns[token_key] < cooldown_to_use:
                logger.info(f"Token {token_key} is in cooldown period. Skipping.")
                return False
            else:
                logger.info(f"Token {token_key} cooldown expired. Processing signal.")

        # Make sure we update the token cooldown
        self.cooldowns[token_key] = now

        # Get channel ID if available
        channel_id = signal.get('channel', signal.get('channel_id', ''))
        if not channel_id:
            # No channel ID available, skip channel cooldown check
            return True

        # Also add channel field for compatibility
        signal['channel'] = channel_id

        # Check if this is a priority channel
        is_priority_channel = False
        if hasattr(self, 'telegram_settings') and isinstance(self.telegram_settings, dict):
            priority_channels = self.telegram_settings.get('priority_channels', [])

            # REMOVED: Automatic channel additions - only use explicitly configured target_channels
            # No automatic priority channel additions - respect user configuration

            # Check if this channel is in priority list
            if channel_id in priority_channels:
                is_priority_channel = True
                logger.info(f"Channel {channel_id} is a priority channel - bypassing channel cooldown")

        # Check channel cooldown (skip for priority channels)
        if not is_priority_channel and channel_id in self.channel_cooldowns:
            if now - self.channel_cooldowns[channel_id] < self.channel_cooldown_period:
                logger.info(f"Channel {channel_id} is in cooldown period. Skipping.")
                return False

        # Update channel cooldown (even for priority channels to track last use)
        self.channel_cooldowns[channel_id] = now

        return True

    async def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate signal based on source and content.
        NOW: Validates primarily on presence of token_address and essential raw metrics if applicable.
        Confidence is no longer pre-assigned here.
        """
        token_address = signal.get('token_address')
        source_type = signal.get('source_type')
        # Log validation attempt
        logger.info(f"Validating signal for token {token_address} from source {source_type}")

        # Basic validation: must have a token address of correct length
        if not token_address or not (32 <= len(token_address) <= 44): # Common Solana address lengths
            logger.warning(f"Invalid or missing token_address for validation: {token_address}")
            return False

        # Source-specific validation (can be expanded to check for key text_metrics)
        # For now, these are simplified as confidence is handled later.
        # They mainly ensure the source type is recognized and basic structure is okay.

        if source_type == 'Solbix Community Calls':
            # Solbix Community Calls provides CA directly in messages
            return await self._validate_solbix(signal)
        elif source_type == 'early':
            return await self._validate_early(signal)
        elif source_type == 'soul_sniper':
            return await self._validate_soul_sniper(signal)
        elif source_type == 'generic_link':
            # Generic links might have less stringent raw metric requirements
            return await self._validate_generic_link(signal)
        elif source_type == 'telegram_basic':
            # Basic signals (just an address) might pass if address is valid
            return await self._validate_basic(signal)
        else:
            logger.warning(f"Unknown source_type for validation: {source_type} for token {token_address}")
            return False # Or True if we want to pass unknown sources for analysis



    async def _validate_solbix(self, signal: Dict[str, Any]) -> bool:
        """Validate Solbix Community Calls signal with direct CA extraction."""
        token_address = signal.get('token_address')
        text_metrics = signal.get('text_metrics', {})

        # Set source_channel if not already set
        if not signal.get('source_channel'):
            signal['source_channel'] = 'Solbix Community Calls'
            logger.info(f"Setting source_channel to 'Solbix Community Calls' for token {token_address}")

        # Set source_channel_id if not already set
        if not signal.get('source_channel_id'):
            signal['source_channel_id'] = -1002428819353
            logger.info(f"Setting source_channel_id to -1002428819353 for token {token_address}")

        # Solbix Community Calls provides CA directly in messages - no hyperlink extraction needed
        logger.info(f"Solbix Community Calls signal detected for token: {token_address}")
        logger.info("CA is directly available in message text - no URL parsing required")

        # Check for liquidity if available in text metrics
        if 'liquidity_usd_value' in text_metrics:
            liquidity = text_metrics['liquidity_usd_value']
            if liquidity < 8000:  # Less than 8000 is risky (user's threshold)
                logger.warning(f"Solbix signal for {token_address} has low liquidity: ${liquidity}")
                # We still pass it through but with a warning

        # Check for volume if available in text metrics
        if 'volume_value' in text_metrics:
            volume = text_metrics['volume_value']
            if volume < 10000:  # Less than 10000 is risky (user's threshold)
                logger.warning(f"Solbix signal for {token_address} has low volume: ${volume}")
                # We still pass it through but with a warning

        # Check for market cap if available in text metrics
        if 'mcp_value' in text_metrics:
            market_cap = text_metrics['mcp_value']
            if market_cap < 14000:  # Less than 14000 is risky (user's updated threshold)
                logger.warning(f"Solbix signal for {token_address} has low market cap: ${market_cap}")
                # We still pass it through but with a warning

        logger.debug(f"Validation for Solbix Community Calls signal for {token_address} passed in SignalHandler.")
        return True  # Pass to BotController for full analysis

    async def _validate_early(self, signal: Dict[str, Any]) -> bool:
        """Validate Early Trending signal with improved metrics extraction."""
        token_address = signal.get('token_address')
        text_metrics = signal.get('text_metrics', {})

        # Set source_channel if not already set
        if not signal.get('source_channel'):
            signal['source_channel'] = 'solana early trending'
            logger.info(f"Setting source_channel to 'solana early trending' for token {token_address}")

        # Check for liquidity - important for Early Trending channel
        if 'liquidity_usd_value' in text_metrics:
            liquidity = text_metrics['liquidity_usd_value']
            if liquidity < 8000:  # Less than 8000 is risky (user's threshold)
                logger.warning(f"Early Trending signal for {token_address} has low liquidity: ${liquidity}")
                # We still pass it through but with a warning

        # Check for volume - important for Early Trending channel
        if 'volume_value' in text_metrics:
            volume = text_metrics['volume_value']
            if volume < 10000:  # Less than 10000 is risky (user's threshold)
                logger.warning(f"Early Trending signal for {token_address} has low volume: ${volume}")
                # We still pass it through but with a warning

        logger.debug(f"Validation for Early Trending signal for {token_address} passed in SignalHandler.")
        return True  # Pass to BotController for full analysis

    async def _validate_soul_sniper(self, signal: Dict[str, Any]) -> bool:
        """Validate Soul Sniper signal with improved metrics extraction."""
        token_address = signal.get('token_address')
        text_metrics = signal.get('text_metrics', {})

        # Set source_channel if not already set
        if not signal.get('source_channel'):
            signal['source_channel'] = 'soul sniper'
            logger.info(f"Setting source_channel to 'soul sniper' for token {token_address}")

        # Check for liquidity - important for Soul Sniper channel
        if 'liquidity_usd_value' in text_metrics:
            liquidity = text_metrics['liquidity_usd_value']
            if liquidity < 8000:  # Less than 8000 is risky (user's threshold)
                logger.warning(f"Soul Sniper signal for {token_address} has low liquidity: ${liquidity}")
                # We still pass it through but with a warning

        # Check for volume - important for Soul Sniper channel
        if 'volume_value' in text_metrics:
            volume = text_metrics['volume_value']
            if volume < 10000:  # Less than 10000 is risky (user's threshold)
                logger.warning(f"Soul Sniper signal for {token_address} has low volume: ${volume}")
                # We still pass it through but with a warning

        logger.debug(f"Validation for Soul Sniper signal for {token_address} passed in SignalHandler.")
        return True  # Pass to BotController for full analysis

    async def _validate_generic_link(self, signal: Dict[str, Any]) -> bool:
        """Validate generic link signal with improved metrics extraction."""
        token_address = signal.get('token_address')
        text_metrics = signal.get('text_metrics', {})

        # Set source_channel if not already set
        if not signal.get('source_channel'):
            signal['source_channel'] = 'generic link'
            logger.info(f"Setting source_channel to 'generic link' for token {token_address}")

        # Check for liquidity - important for generic links
        if 'liquidity_usd_value' in text_metrics:
            liquidity = text_metrics['liquidity_usd_value']
            if liquidity < 8000:  # Less than 8000 is risky (user's threshold)
                logger.warning(f"Generic link signal for {token_address} has low liquidity: ${liquidity}")
                # We still pass it through but with a warning

        # Check for volume - important for generic links
        if 'volume_value' in text_metrics:
            volume = text_metrics['volume_value']
            if volume < 10000:  # Less than 10000 is risky (user's threshold)
                logger.warning(f"Generic link signal for {token_address} has low volume: ${volume}")
                # We still pass it through but with a warning

        logger.debug(f"Validation for generic link signal for {token_address} passed in SignalHandler.")
        return True  # Pass to BotController for full analysis

    async def _validate_basic(self, signal: Dict[str, Any]) -> bool:
        """Validate basic signal (e.g., just a token address found) with improved metrics extraction."""
        token_address = signal.get('token_address')
        text_metrics = signal.get('text_metrics', {})

        # Set source_channel if not already set
        if not signal.get('source_channel'):
            signal['source_channel'] = 'telegram basic'
            logger.info(f"Setting source_channel to 'telegram basic' for token {token_address}")

        # Check for liquidity - important for basic signals
        if 'liquidity_usd_value' in text_metrics:
            liquidity = text_metrics['liquidity_usd_value']
            if liquidity < 8000:  # Less than 8000 is risky (user's threshold)
                logger.warning(f"Basic signal for {token_address} has low liquidity: ${liquidity}")
                # We still pass it through but with a warning

        # Check for volume - important for basic signals
        if 'volume_value' in text_metrics:
            volume = text_metrics['volume_value']
            if volume < 10000:  # Less than 10000 is risky (user's threshold)
                logger.warning(f"Basic signal for {token_address} has low volume: ${volume}")
                # We still pass it through but with a warning

        logger.debug(f"Validation for basic signal for {token_address} passed in SignalHandler.")
        return True  # Pass to BotController for full analysis

    def _extract_metrics(self, text: str) -> Dict[str, str]:
        """
        Extract metrics from message text with comprehensive pattern matching.

        This enhanced implementation includes:
        - Support for multiple message formats (Donald Calls, High Volume Alert, Pepe Calls, OxyZen, Solana Early Trending, GMGN)
        - Improved extraction of token metrics (price, volume, liquidity, market cap, etc.)
        - Better handling of numeric values with K/M/B suffixes

        Args:
            text: The message text to extract metrics from

        Returns:
            Dictionary of extracted metrics with normalized values
        """
        metrics = {'message_text': text}

        # Check for different message formats and apply appropriate extraction

        # Check for Donald Calls format ($MEGG)
        if re.search(r'#SOL\s+DONALD|\$MEGG', text, re.IGNORECASE):
            logger.info("Detected Donald Calls format")
            # Extract token symbol
            token_match = TOKEN_SYMBOL_REGEX.search(text)
            if token_match:
                metrics['token_name'] = token_match.group(1)
                logger.info(f"Found token symbol: {metrics['token_name']}")

            # Extract market cap
            mcap_match = MCP_REGEX.search(text)
            if mcap_match:
                mcp_str = mcap_match.group(1)
                # Parse the value to handle K/M/B suffixes
                mcp_value = self._parse_numeric_value(mcp_str, 'marketcap')
                metrics['mcp_value'] = mcp_value
                metrics['mcp'] = self._format_large_number(mcp_value)
                logger.info(f"Found market cap: {mcp_str} - {metrics['mcp']}")

            # Extract age
            age_match = AGE_REGEX.search(text)
            if age_match:
                metrics['age'] = re.search(r'Age:?\s*(\d{1,3})', text, re.IGNORECASE).group(1)
                logger.info(f"Found age: {metrics['age']} minutes")

            # Extract LP Burn status
            lp_burn_match = LP_BURN_REGEX.search(text)
            if lp_burn_match:
                metrics['lp_burn'] = lp_burn_match.group(1)
                logger.info(f"Found LP burn status: {metrics['lp_burn']}")

            # Extract top holders
            top_holders_match = TOP_HOLDERS_REGEX.search(text)
            if top_holders_match:
                metrics['top_holders'] = re.search(r'(?:TOP\s*10|Top\s*10)[: ]+(\d{1,3}(?:\.\d+)?)%', text, re.IGNORECASE).group(1) + "%"
                logger.info(f"Found top holders: {metrics['top_holders']}")

            # Extract risk rating
            risk_match = RISK_RATING_REGEX.search(text)
            if risk_match:
                metrics['risk_rating'] = risk_match.group(1)
                logger.info(f"Found risk rating: {metrics['risk_rating']}")

            # Extract contract address
            ca_match = CA_LINE_REGEX.search(text)
            if ca_match:
                metrics['contract_address'] = ca_match.group(1)
                logger.info(f"Found contract address: {metrics['contract_address']}")

            # Extract volume
            volume_match = VOLUME_REGEX.search(text)
            if volume_match:
                vol_str = re.search(r'24H\s*VOL[: ]+\$?(\d{1,6}[KM])', text, re.IGNORECASE).group(1)
                # Parse the value to handle K/M/B suffixes
                vol_value = self._parse_numeric_value(vol_str, 'volume')
                metrics['volume_value'] = vol_value
                metrics['volume'] = self._format_large_number(vol_value)
                logger.info(f"Found volume: {vol_str} - {metrics['volume']}")

        # Check for High Volume Alert format ($TOYS)
        elif re.search(r'High\s+Volume\s+Alert|\$TOYS|Token\s*Symbol', text, re.IGNORECASE):
            logger.info("Detected High Volume Alert format")
            # Extract token symbol using standard regex
            token_match = TOKEN_SYMBOL_REGEX.search(text)
            if token_match:
                metrics['token_name'] = token_match.group(1)
                logger.info(f"Found token symbol: {metrics['token_name']}")

            # Extract market cap
            mcap_match = MCP_REGEX.search(text)
            if mcap_match:
                mcp_str = mcap_match.group(1)
                # Parse the value to handle K/M/B suffixes
                mcp_value = self._parse_numeric_value(mcp_str, 'marketcap')
                metrics['mcp_value'] = mcp_value
                metrics['mcp'] = self._format_large_number(mcp_value)
                logger.info(f"Found market cap: {mcp_str} - {metrics['mcp']}")

            # Extract age
            age_match = AGE_REGEX.search(text)
            if age_match:
                metrics['age'] = re.search(r'Age:?\s*(\d{1,3})', text, re.IGNORECASE).group(1)
                logger.info(f"Found age: {metrics['age']} minutes")

            # Extract LP Burn status
            lp_burn_match = LP_BURN_REGEX.search(text)
            if lp_burn_match:
                metrics['lp_burn'] = lp_burn_match.group(1)
                logger.info(f"Found LP burn status: {metrics['lp_burn']}")

            # Extract top holders
            top_holders_match = TOP_HOLDERS_REGEX.search(text)
            if top_holders_match:
                metrics['top_holders'] = re.search(r'(?:TOP\s*10|Top\s*10)[: ]+(\d{1,3}(?:\.\d+)?)%', text, re.IGNORECASE).group(1) + "%"
                logger.info(f"Found top holders: {metrics['top_holders']}")

            # Extract risk rating
            risk_match = RISK_RATING_REGEX.search(text)
            if risk_match:
                metrics['risk_rating'] = risk_match.group(1)
                logger.info(f"Found risk rating: {metrics['risk_rating']}")

            # Extract contract address
            ca_match = CA_LINE_REGEX.search(text)
            if ca_match:
                metrics['contract_address'] = ca_match.group(1)
                logger.info(f"Found contract address: {metrics['contract_address']}")

            # Extract volume
            volume_match = VOLUME_REGEX.search(text)
            if volume_match:
                vol_str = re.search(r'24H\s*VOL[: ]+\$?(\d{1,6}[KM])', text, re.IGNORECASE).group(1)
                # Parse the value to handle K/M/B suffixes
                vol_value = self._parse_numeric_value(vol_str, 'volume')
                metrics['volume_value'] = vol_value
                metrics['volume'] = self._format_large_number(vol_value)
                logger.info(f"Found volume: {vol_str} - {metrics['volume']}")

        # Check for Pepe Calls format ($CV)
        elif re.search(r'Pepe\s+Calls|\$CV|Trojan', text, re.IGNORECASE):
            logger.info("Detected Pepe Calls format")
            # Extract token symbol
            token_match = TOKEN_SYMBOL_REGEX.search(text)
            if token_match:
                metrics['token_name'] = token_match.group(1)
                logger.info(f"Found token symbol: {metrics['token_name']}")

            # Extract market cap
            mcap_match = MCP_REGEX.search(text)
            if mcap_match:
                mcp_str = mcap_match.group(1)
                # Parse the value to handle K/M/B suffixes
                mcp_value = self._parse_numeric_value(mcp_str, 'marketcap')
                metrics['mcp_value'] = mcp_value
                metrics['mcp'] = self._format_large_number(mcp_value)
                logger.info(f"Found market cap: {mcp_str} - {metrics['mcp']}")

            # Extract liquidity
            liq_match = LIQUIDITY_REGEX.search(text)
            if liq_match:
                liq_str = re.search(r'Liq:?\s*\$?(\d{1,6}[KM])', text, re.IGNORECASE).group(1)
                # Parse the value to handle K/M/B suffixes
                liq_value = self._parse_numeric_value(liq_str, 'liquidity')
                metrics['liquidity_usd_value'] = liq_value
                metrics['liquidity_usd'] = self._format_large_number(liq_value)
                logger.info(f"Found liquidity: {liq_str} - {metrics['liquidity_usd']}")

            # Extract volume
            vol_match = VOLUME_1H_REGEX.search(text)
            if vol_match:
                vol_str = re.search(r'Vol:?\s*1h:?\s*\$?(\d{1,6}[KM])', text, re.IGNORECASE).group(1)
                # Parse the value to handle K/M/B suffixes
                vol_value = self._parse_numeric_value(vol_str, 'volume')
                metrics['volume_value'] = vol_value
                metrics['volume'] = self._format_large_number(vol_value)
                logger.info(f"Found volume: {vol_str} - {metrics['volume']}")

            # Extract holders
            holders_match = HOLDERS_REGEX.search(text)
            if holders_match:
                metrics['holders'] = re.search(r'Hodls:?\s*(\d{1,5})', text, re.IGNORECASE).group(1)
                logger.info(f"Found holders: {metrics['holders']}")

            # Extract dev status
            dev_match = DEV_STATUS_REGEX.search(text)
            if dev_match:
                metrics['dev_actions'] = re.search(r'Dev:?\s*([0-9.]+\s*SOL\s*\|\s*0%\s*\$[A-Z0-9]{2,10})', text, re.IGNORECASE).group(1)
                logger.info(f"Found dev status: {metrics['dev_actions']}")

            # Extract sold percentage
            sold_match = SOLD_REGEX.search(text)
            if sold_match:
                metrics['sold'] = re.search(r'Sold:?\s*(\d{1,3}\.\d{1,2})%', text, re.IGNORECASE).group(1) + "%"
                logger.info(f"Found sold percentage: {metrics['sold']}")

        # Check for OxyZen format ($BUTTERCOIN)
        elif re.search(r'OxyZen|\$BUTTERCOIN', text, re.IGNORECASE):
            logger.info("Detected OxyZen format")
            # Extract token symbol (longer format for BUTTERCOIN)
            token_match = TOKEN_SYMBOL_LONG_REGEX.search(text)
            if token_match:
                metrics['token_name'] = token_match.group(1)
                logger.info(f"Found token symbol: {metrics['token_name']}")

            # Extract contract address
            ca_match = CA_LINE_REGEX.search(text)
            if ca_match:
                metrics['contract_address'] = ca_match.group(1)
                logger.info(f"Found contract address: {metrics['contract_address']}")

            # Extract DEX URL
            dex_url_match = DEX_URL_REGEX.search(text)
            if dex_url_match:
                metrics['dex_url'] = dex_url_match.group(1)
                logger.info(f"Found DEX URL: {metrics['dex_url']}")

            # Extract chain
            chain_match = CHAIN_SOL_REGEX.search(text)
            if chain_match:
                metrics['chain'] = 'SOL'
                logger.info(f"Found chain: SOL")

        # Check for Solbix Community Calls format
        elif re.search(r'Solbix|Community\s+Calls', text, re.IGNORECASE):
            logger.info("Detected Solbix Community Calls format")

            # Extract token symbol - use general pattern since format may vary
            token_match = TOKEN_SYMBOL_REGEX.search(text)
            if token_match:
                metrics['token_name'] = token_match.group(1)
                logger.info(f"Found token symbol: {metrics['token_name']}")

            # Extract contract address - CA is directly available in message
            ca_match = CA_LINE_REGEX.search(text)
            if ca_match:
                metrics['contract_address'] = ca_match.group(1)
                logger.info(f"Found contract address: {metrics['contract_address']}")
            else:
                # Try alternative CA patterns for Solbix format
                ca_alt_match = re.search(r'(?:CA|Contract|Address)[:\s]*([1-9A-HJ-NP-Za-km-z]{32,44}(?:pump)?)', text, re.IGNORECASE)
                if ca_alt_match:
                    metrics['contract_address'] = ca_alt_match.group(1)
                    logger.info(f"Found contract address (alt pattern): {metrics['contract_address']}")

            # Extract market cap if available
            mcap_match = MCP_REGEX.search(text)
            if mcap_match:
                mcp_str = mcap_match.group(1)
                mcp_value = self._parse_numeric_value(mcp_str, 'marketcap')
                metrics['mcp_value'] = mcp_value
                metrics['mcp'] = self._format_large_number(mcp_value)
                logger.info(f"Found market cap: {mcp_str} - {metrics['mcp']}")

            # Extract liquidity if available
            liq_match = LIQUIDITY_REGEX.search(text)
            if liq_match:
                liq_str = liq_match.group(1)
                liq_value = self._parse_numeric_value(liq_str, 'liquidity')
                metrics['liquidity_usd_value'] = liq_value
                metrics['liquidity_usd'] = self._format_large_number(liq_value)
                logger.info(f"Found liquidity: {liq_str} - {metrics['liquidity_usd']}")

            # Extract volume if available
            volume_match = VOLUME_REGEX.search(text)
            if volume_match:
                vol_str = volume_match.group(1)
                vol_value = self._parse_numeric_value(vol_str, 'volume')
                metrics['volume_value'] = vol_value
                metrics['volume'] = self._format_large_number(vol_value)
                logger.info(f"Found volume: {vol_str} - {metrics['volume']}")



        # Always return metrics dictionary, even if empty
        return metrics

    def _format_large_number(self, value: float) -> str:
        """
        Format a large number with K/M/B suffix for human-readable output.

        Args:
            value: The numeric value to format

        Returns:
            Formatted string with appropriate suffix
        """
        try:
            if not isinstance(value, (int, float)):
                return "0"

            if value >= 1_000_000_000:
                return f"{value / 1_000_000_000:.2f}B"
            elif value >= 1_000_000:
                return f"{value / 1_000_000:.2f}M"
            elif value >= 1_000:
                return f"{value / 1_000:.2f}K"
            else:
                return f"{value:.2f}"
        except Exception as e:
            logger.error(f"Error formatting large number: {e}")
            return str(value)
