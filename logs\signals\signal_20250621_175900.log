2025-06-21 17:59:00,820 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 17:59:00,821 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 17:59:00,821 - signal_handler - INFO - Loaded session string from C:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 17:59:00,821 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750508940
2025-06-21 17:59:00,821 - signal_handler - INFO - Using session path: C:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750508940
2025-06-21 17:59:00,821 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 17:59:00,828 - signal_handler - INFO - Direct signal callback registered
2025-06-21 17:59:00,830 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 17:59:00,830 - signal_handler - INFO - API ID loaded: True
2025-06-21 17:59:00,830 - signal_handler - INFO - API Hash loaded: True
2025-06-21 17:59:00,830 - signal_handler - INFO - Phone number loaded: True
2025-06-21 17:59:00,830 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 17:59:00,830 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 17:59:00,831 - signal_handler - INFO - Using saved session string
2025-06-21 17:59:00,831 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 17:59:02,032 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:59:02,231 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:59:02,231 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 17:59:02,232 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:59:02,428 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:59:02,429 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 17:59:02,429 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:59:02,628 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:59:02,629 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 17:59:02,629 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 17:59:02,629 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 17:59:03,063 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 17:59:03,063 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 17:59:03,063 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 17:59:03,064 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 17:59:03,065 - signal_handler - INFO - Direct signal callback registered
2025-06-21 17:59:03,328 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 17:59:03,445 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 17:59:03,445 - signal_handler - INFO - API ID loaded: True
2025-06-21 17:59:03,445 - signal_handler - INFO - API Hash loaded: True
2025-06-21 17:59:03,446 - signal_handler - INFO - Phone number loaded: True
2025-06-21 17:59:03,446 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 17:59:03,446 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 17:59:03,446 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:59:03,646 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:59:03,646 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 17:59:03,646 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:59:03,843 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:59:03,843 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 17:59:03,843 - signal_handler - INFO - Checking authorization status...
2025-06-21 17:59:04,057 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 17:59:04,057 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 17:59:04,058 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 17:59:04,058 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 17:59:04,287 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 17:59:04,290 - signal_handler - INFO - [msg_1750508944288_5315] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 17:59:04
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 17:59:04,290 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 17:59:04,290 - signal_handler - INFO - [msg_1750508944288_5315] Message queued with normal priority
2025-06-21 17:59:04,291 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 17:59:04,291 - signal_handler - INFO - [msg_1750508944288_5315] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:59:04,491 - signal_handler - INFO - [msg_1750508944288_5315] Successfully resolved info channel entity: Bot Info
2025-06-21 17:59:04,718 - signal_handler - INFO - [msg_1750508944288_5315] Message sent directly successfully using entity
2025-06-21 17:59:04,718 - signal_handler - INFO - [msg_1750508944288_5315] Message sent successfully via direct send method
2025-06-21 17:59:04,718 - signal_handler - INFO - [msg_1750508944288_5315] Message sent successfully on attempt 1
2025-06-21 17:59:04,793 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 17:59:04,794 - signal_handler - INFO - Direct signal callback registered
2025-06-21 17:59:04,794 - signal_handler - INFO - [msg_1750508944794_7991] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: default
[MONEY] Starting Capital: 1.5 SO...
2025-06-21 17:59:04,794 - signal_handler - INFO - [msg_1750508944794_7991] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:59:04,993 - signal_handler - INFO - [msg_1750508944794_7991] Successfully resolved info channel entity: Bot Info
2025-06-21 17:59:05,223 - signal_handler - INFO - [msg_1750508944794_7991] Message sent directly successfully using entity
2025-06-21 17:59:05,223 - signal_handler - INFO - [msg_1750508944794_7991] Message sent directly successfully
2025-06-21 17:59:08,451 - signal_handler - INFO - [msg_1750508948451_2369] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 17:59:08

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 17:59:08,451 - signal_handler - INFO - [msg_1750508948451_2369] Message queued with normal priority
2025-06-21 17:59:08,476 - signal_handler - INFO - [msg_1750508948476_2563] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 17:59:08
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 17:59:08,476 - signal_handler - INFO - [msg_1750508948476_2563] Message queued with normal priority
2025-06-21 17:59:08,633 - signal_handler - INFO - [msg_1750508948451_2369] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:59:08,832 - signal_handler - INFO - [msg_1750508948451_2369] Successfully resolved info channel entity: Bot Info
2025-06-21 17:59:09,058 - signal_handler - INFO - [msg_1750508948451_2369] Message sent directly successfully using entity
2025-06-21 17:59:09,058 - signal_handler - INFO - [msg_1750508948451_2369] Message sent successfully via direct send method
2025-06-21 17:59:09,058 - signal_handler - INFO - [msg_1750508948451_2369] Message sent successfully on attempt 1
2025-06-21 17:59:09,058 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-21 17:59:09,058 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 17:59:09,568 - signal_handler - INFO - [msg_1750508948476_2563] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:59:09,770 - signal_handler - INFO - [msg_1750508948476_2563] Successfully resolved info channel entity: Bot Info
2025-06-21 17:59:09,999 - signal_handler - INFO - [msg_1750508948476_2563] Message sent directly successfully using entity
2025-06-21 17:59:09,999 - signal_handler - INFO - [msg_1750508948476_2563] Message sent successfully via direct send method
2025-06-21 17:59:09,999 - signal_handler - INFO - [msg_1750508948476_2563] Message sent successfully on attempt 1
2025-06-21 17:59:11,689 - signal_handler - INFO - Received message from channel -1002017173747: [ROCKET] NEW SOL HIGH VOLUME TOKEN [ROCKET]

            [TARGET] Token Symbol: $$
             MCAP...
2025-06-21 17:59:11,689 - signal_handler - INFO - Setting channel_identifier to 'SOL HIGH VOLUME ALERT | Bordga' based on chat_id -1002017173747
2025-06-21 17:59:11,690 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 17:59:11,691 - signal_handler - INFO - Found contract address with 'pump' suffix: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 17:59:11,691 - signal_handler - INFO - Found contract address: etcwt6fpliaa5s9az5akmjvum1pz6eye1fawphremf9z
2025-06-21 17:59:11,691 - signal_handler - INFO - Using 'pump' address as highest priority: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 17:59:11,692 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 17:59:11,692 - signal_handler - INFO - Detected High Volume Alert format
2025-06-21 17:59:11,692 - signal_handler - INFO - Found market cap: 66K - 66.00K
2025-06-21 17:59:11,693 - signal_handler - INFO - Found age: 4 minutes
2025-06-21 17:59:11,693 - signal_handler - INFO - Found top holders: 21.18%
2025-06-21 17:59:11,693 - signal_handler - INFO - Found risk rating: Good
2025-06-21 17:59:11,693 - signal_handler - INFO - Detected signal from SOL HIGH VOLUME ALERT | Bordga for token: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 17:59:11,694 - signal_handler - INFO - Extracted signal: Token=5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump, Source=SOL HIGH VOLUME ALERT | Bordga, Metrics Count=6
2025-06-21 17:59:11,694 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-21 17:59:11,694 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-21 17:59:11,694 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-21 17:59:11,694 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-21 17:59:11,694 - signal_handler - INFO - Added signal to queue: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump from SOL HIGH VOLUME ALERT | Bordga with confidence 0.5, GMGN channel: False
2025-06-21 17:59:11,695 - signal_handler - INFO - Calling direct signal callback for 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 17:59:11,697 - signal_handler - INFO - Direct signal processing completed for 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump
2025-06-21 17:59:11,698 - signal_handler - INFO - Signal forwarded to bot controller: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump from SOL HIGH VOLUME ALERT | Bordga (confidence: 0.50)
2025-06-21 17:59:11,698 - signal_handler - INFO - [msg_1750508951698_6736] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 17:59:11

 Token CA: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjp...
2025-06-21 17:59:11,698 - signal_handler - INFO - [msg_1750508951698_6736] Message queued with normal priority
2025-06-21 17:59:12,729 - signal_handler - INFO - Retrieved signal from queue: 5LN9HNUwbHjSvftv7xP6wLUsyz4B73pMWcdk9VXjpump from SOL HIGH VOLUME ALERT | Bordga
2025-06-21 17:59:12,732 - signal_handler - INFO - [msg_1750508951698_6736] Resolving info channel entity for ID: -1002362136450
2025-06-21 17:59:13,311 - signal_handler - INFO - [msg_1750508951698_6736] Successfully resolved info channel entity: Bot Info
2025-06-21 17:59:13,543 - signal_handler - INFO - [msg_1750508951698_6736] Message sent directly successfully using entity
2025-06-21 17:59:13,544 - signal_handler - INFO - [msg_1750508951698_6736] Message sent successfully via direct send method
2025-06-21 17:59:13,544 - signal_handler - INFO - [msg_1750508951698_6736] Message sent successfully on attempt 1
2025-06-21 17:59:25,796 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
