2025-06-21 23:10:25,250 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 23:10:25,250 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 23:10:25,250 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 23:10:25,251 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750527625
2025-06-21 23:10:25,251 - signal_handler - INFO - Using session path: c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750527625
2025-06-21 23:10:25,251 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 23:10:25,260 - signal_handler - INFO - Direct signal callback registered
2025-06-21 23:10:25,261 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 23:10:25,261 - signal_handler - INFO - API ID loaded: True
2025-06-21 23:10:25,261 - signal_handler - INFO - API Hash loaded: True
2025-06-21 23:10:25,261 - signal_handler - INFO - Phone number loaded: True
2025-06-21 23:10:25,261 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 23:10:25,262 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 23:10:25,262 - signal_handler - INFO - Using saved session string
2025-06-21 23:10:25,262 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 23:10:25,567 - signal_handler - INFO - Checking authorization status...
2025-06-21 23:10:25,615 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 23:10:25,615 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 23:10:25,616 - signal_handler - INFO - Checking authorization status...
2025-06-21 23:10:25,663 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 23:10:25,664 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 23:10:25,664 - signal_handler - INFO - Checking authorization status...
2025-06-21 23:10:25,711 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 23:10:25,711 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 23:10:25,711 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 23:10:25,711 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 23:10:25,856 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 23:10:25,857 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 23:10:25,857 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 23:10:25,857 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 23:10:25,859 - signal_handler - INFO - Direct signal callback registered
2025-06-21 23:10:26,056 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 23:10:42,109 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 23:10:42,109 - signal_handler - INFO - API ID loaded: True
2025-06-21 23:10:42,109 - signal_handler - INFO - API Hash loaded: True
2025-06-21 23:10:42,109 - signal_handler - INFO - Phone number loaded: True
2025-06-21 23:10:42,109 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 23:10:42,110 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 23:10:42,110 - signal_handler - INFO - Checking authorization status...
2025-06-21 23:10:42,165 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 23:10:42,165 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 23:10:42,166 - signal_handler - INFO - Checking authorization status...
2025-06-21 23:10:42,217 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 23:10:42,217 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 23:10:42,217 - signal_handler - INFO - Checking authorization status...
2025-06-21 23:10:42,274 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 23:10:42,274 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 23:10:42,275 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 23:10:42,275 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 23:10:42,361 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 23:10:42,366 - signal_handler - INFO - [msg_1750527642364_6602] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 23:10:42
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 23:10:42,366 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 23:10:42,366 - signal_handler - INFO - [msg_1750527642364_6602] Message queued with normal priority
2025-06-21 23:10:42,367 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 23:10:42,367 - signal_handler - INFO - [msg_1750527642364_6602] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:10:42,417 - signal_handler - INFO - [msg_1750527642364_6602] Successfully resolved info channel entity: Bot Info
2025-06-21 23:10:42,510 - signal_handler - INFO - [msg_1750527642364_6602] Message sent directly successfully using entity
2025-06-21 23:10:42,510 - signal_handler - INFO - [msg_1750527642364_6602] Message sent successfully via direct send method
2025-06-21 23:10:42,511 - signal_handler - INFO - [msg_1750527642364_6602] Message sent successfully on attempt 1
2025-06-21 23:10:42,872 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 23:10:42,872 - signal_handler - INFO - Direct signal callback registered
2025-06-21 23:10:42,873 - signal_handler - INFO - [msg_1750527642873_4503] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capital: 0.0...
2025-06-21 23:10:42,873 - signal_handler - INFO - [msg_1750527642873_4503] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:10:42,925 - signal_handler - INFO - [msg_1750527642873_4503] Successfully resolved info channel entity: Bot Info
2025-06-21 23:10:43,007 - signal_handler - INFO - [msg_1750527642873_4503] Message sent directly successfully using entity
2025-06-21 23:10:43,008 - signal_handler - INFO - [msg_1750527642873_4503] Message sent directly successfully
2025-06-21 23:11:42,225 - signal_handler - INFO - Received message from channel -1001509251052: [ALARM] NEW POTENTIAL GEM!

**Gorbagana Acceleration** [?] **$gor/acc**
`31TxE8kyhUJfq8JPeJNTdVPZeib...
2025-06-21 23:11:42,225 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 23:11:42,227 - signal_handler - INFO - Found contract address with 'pump' suffix: ********************************************
2025-06-21 23:11:42,227 - signal_handler - INFO - Found contract address: 7G3zKutx3W4cqBbX3gwBhe1DkoaK7kP4KfCVxZnfAdu4
2025-06-21 23:11:42,228 - signal_handler - INFO - Found contract address: GJA1HEbxGnqBhBifH9uQauzXSB53to5rhDrzmKxhSU65
2025-06-21 23:11:42,228 - signal_handler - INFO - Found contract address: HrYrqJoPg56TqAmBvdtrM2R97LiCTYZZR21afg42ha3T
2025-06-21 23:11:42,228 - signal_handler - INFO - Found contract address: 2EafEQsnTiPD81DeAtqoVATcb4tZGhNt8zwgw9Q3Q1Cr
2025-06-21 23:11:42,228 - signal_handler - INFO - Found contract address: Hxmm3vjvR9iRSHDhn7qvR48CVZJPyQ4FoKH5kTD3HaMM
2025-06-21 23:11:42,229 - signal_handler - INFO - Using 'pump' address as highest priority: ********************************************
2025-06-21 23:11:42,229 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-21 23:11:42,229 - signal_handler - INFO - Detected Pepe Calls format
2025-06-21 23:11:42,229 - signal_handler - INFO - Found token symbol: gor
2025-06-21 23:11:42,230 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=None, Metrics Count=2
2025-06-21 23:11:42,230 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-21 23:11:42,230 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-21 23:11:42,230 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-21 23:11:42,230 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-21 23:11:42,230 - signal_handler - INFO - Added signal to queue: ******************************************** from None with confidence 0.5, GMGN channel: False
2025-06-21 23:11:42,230 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-21 23:11:42,233 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-21 23:11:42,233 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from None (confidence: 0.50)
2025-06-21 23:11:43,697 - signal_handler - INFO - Retrieved signal from queue: ******************************************** from None
2025-06-21 23:11:49,147 - signal_handler - INFO - [msg_1750527709146_5814] [SELL NOTIFICATION]: [GREEN] [BUY EXECUTED] [?] 2025-06-21 23:11:49

 Signal Source: None
 Token: 31TxE8kyhUJfq8JPeJNTdVP...
2025-06-21 23:11:49,148 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750527709146_5814.txt
2025-06-21 23:11:49,148 - signal_handler - INFO - [msg_1750527709146_5814] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:11:49,200 - signal_handler - INFO - [msg_1750527709146_5814] Successfully resolved info channel entity: Bot Info
2025-06-21 23:11:49,295 - signal_handler - INFO - [msg_1750527709146_5814] Message sent directly successfully using entity
2025-06-21 23:11:49,296 - signal_handler - INFO - [msg_1750527709146_5814] Message sent directly successfully
2025-06-21 23:11:49,296 - signal_handler - INFO - [msg_1750527709296_1527] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (REAL) - 31TXE8KY

 Timestamp: 2025-06-21 23:11:49
[REPEAT] Trade: R...
2025-06-21 23:11:49,296 - signal_handler - INFO - [msg_1750527709296_1527] Message queued with normal priority
2025-06-21 23:11:49,368 - signal_handler - INFO - [msg_1750527709296_1527] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:11:49,420 - signal_handler - INFO - [msg_1750527709296_1527] Successfully resolved info channel entity: Bot Info
2025-06-21 23:11:49,499 - signal_handler - INFO - [msg_1750527709296_1527] Message sent directly successfully using entity
2025-06-21 23:11:49,499 - signal_handler - INFO - [msg_1750527709296_1527] Message sent successfully via direct send method
2025-06-21 23:11:49,499 - signal_handler - INFO - [msg_1750527709296_1527] Message sent successfully on attempt 1
2025-06-21 23:12:07,667 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.7K(+266%)**
**[...
2025-06-21 23:12:07,668 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 23:12:07,668 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 23:12:07,668 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 23:12:07,668 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 23:12:07,669 - signal_handler - INFO - Found contract address with 'pump' suffix: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,669 - signal_handler - INFO - Found contract address: A9MfryLZseBqmGcvo3USMFeSYKQaQ1CMPZyh5aV9Wc7v
2025-06-21 23:12:07,669 - signal_handler - INFO - Using 'pump' address as highest priority: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,669 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,670 - signal_handler - INFO - Detected GMGN format
2025-06-21 23:12:07,670 - signal_handler - INFO - Found token symbol: 10
2025-06-21 23:12:07,670 - signal_handler - INFO - Found FDV: 10.7K - 10.70 (+266%)
2025-06-21 23:12:07,670 - signal_handler - INFO - Detected GMGN channel signal for token: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,671 - signal_handler - INFO - Extracted signal: Token=5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 23:12:07,671 - signal_handler - INFO - Added signal to queue: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 23:12:07,671 - signal_handler - INFO - Calling direct signal callback for 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,673 - signal_handler - INFO - Direct signal processing completed for 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,673 - signal_handler - INFO - Signal forwarded to bot controller: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 23:12:07,674 - signal_handler - INFO - [msg_1750527727674_1218] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 23:12:07

 Token CA: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGp...
2025-06-21 23:12:07,674 - signal_handler - INFO - [msg_1750527727674_1218] Message queued with normal priority
2025-06-21 23:12:09,925 - signal_handler - INFO - [msg_1750527729925_3601] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-21 23:12:09

 Token: 5dDsticWTu5w6oJbhZQkZCzc33...
2025-06-21 23:12:09,925 - signal_handler - INFO - [msg_1750527729925_3601] Message queued with normal priority
2025-06-21 23:12:09,925 - signal_handler - INFO - Retrieved signal from queue: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump from solana signal alert - gmgn
2025-06-21 23:12:09,927 - signal_handler - INFO - [msg_1750527727674_1218] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:12:10,086 - signal_handler - INFO - [msg_1750527727674_1218] Successfully resolved info channel entity: Bot Info
2025-06-21 23:12:10,161 - signal_handler - INFO - [msg_1750527727674_1218] Message sent directly successfully using entity
2025-06-21 23:12:10,162 - signal_handler - INFO - [msg_1750527727674_1218] Message sent successfully via direct send method
2025-06-21 23:12:10,162 - signal_handler - INFO - [msg_1750527727674_1218] Message sent successfully on attempt 1
2025-06-21 23:12:10,162 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 23:12:10,309 - signal_handler - INFO - Received message from channel -1002177594166: ********************************************...
2025-06-21 23:12:10,310 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-21 23:12:10,310 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 23:12:10,310 - signal_handler - INFO - Found contract address with 'pump' suffix: ********************************************
2025-06-21 23:12:10,310 - signal_handler - INFO - Using 'pump' address as highest priority: ********************************************
2025-06-21 23:12:10,311 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-21 23:12:10,311 - signal_handler - INFO - Detected signal from OxyZen Calls for token: ********************************************
2025-06-21 23:12:10,311 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=OxyZen Calls, Metrics Count=1
2025-06-21 23:12:10,311 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: ********************************************
2025-06-21 23:12:10,311 - signal_handler - INFO - Token ******************************************** cooldown expired. Processing signal.
2025-06-21 23:12:10,312 - signal_handler - INFO - Added signal to queue: ******************************************** from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-21 23:12:10,312 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-21 23:12:10,313 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-21 23:12:10,313 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from OxyZen Calls (confidence: 0.50)
2025-06-21 23:12:10,313 - signal_handler - DEBUG - QUEUE DEDUP: Skipping ******************************************** (processed 26.6s ago)
2025-06-21 23:12:10,313 - signal_handler - INFO - [msg_1750527730313_2662] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 23:12:10

 Token CA: 31TxE8kyhUJfq8JPeJNTdVPZeibQrcNnDbkvnigVp...
2025-06-21 23:12:10,314 - signal_handler - INFO - [msg_1750527730313_2662] Message queued with normal priority
2025-06-21 23:12:10,664 - signal_handler - INFO - [msg_1750527729925_3601] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:12:10,713 - signal_handler - INFO - [msg_1750527729925_3601] Successfully resolved info channel entity: Bot Info
2025-06-21 23:12:10,796 - signal_handler - INFO - [msg_1750527729925_3601] Message sent directly successfully using entity
2025-06-21 23:12:10,797 - signal_handler - INFO - [msg_1750527729925_3601] Message sent successfully via direct send method
2025-06-21 23:12:10,797 - signal_handler - INFO - [msg_1750527729925_3601] Message sent successfully on attempt 1
2025-06-21 23:12:10,797 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 23:12:11,148 - signal_handler - INFO - Received message from channel -1002017857449: ********************************************...
2025-06-21 23:12:11,149 - signal_handler - INFO - Setting channel_identifier to 'PEPE CALLS' based on chat_id -1002017857449
2025-06-21 23:12:11,149 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 23:12:11,149 - signal_handler - INFO - Found contract address with 'pump' suffix: ********************************************
2025-06-21 23:12:11,150 - signal_handler - INFO - Using 'pump' address as highest priority: ********************************************
2025-06-21 23:12:11,150 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-21 23:12:11,150 - signal_handler - INFO - Detected signal from PEPE CALLS for token: ********************************************
2025-06-21 23:12:11,151 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=PEPE CALLS, Metrics Count=1
2025-06-21 23:12:11,151 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: ********************************************
2025-06-21 23:12:11,152 - signal_handler - INFO - Token ******************************************** is in cooldown period. Skipping.
2025-06-21 23:12:11,152 - signal_handler - INFO - Signal dropped due to cooldown: ********************************************
2025-06-21 23:12:11,301 - signal_handler - INFO - [msg_1750527730313_2662] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:12:11,351 - signal_handler - INFO - [msg_1750527730313_2662] Successfully resolved info channel entity: Bot Info
2025-06-21 23:12:11,434 - signal_handler - INFO - [msg_1750527730313_2662] Message sent directly successfully using entity
2025-06-21 23:12:11,434 - signal_handler - INFO - [msg_1750527730313_2662] Message sent successfully via direct send method
2025-06-21 23:12:11,434 - signal_handler - INFO - [msg_1750527730313_2662] Message sent successfully on attempt 1
2025-06-21 23:12:50,771 - signal_handler - INFO - Received sell notification: [PURPLE] [SELL [?] TP1 HIT (32.7% >= 13.0%)] [?] 2025-06-21 23:12:50
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Triggere...
2025-06-21 23:12:50,772 - signal_handler - INFO - [msg_1750527770771_9199] [SELL NOTIFICATION]:  [SELL [?] TP1 HIT (32.7% >= 13.0%)] [?] 2025-06-21 23:12:50

 Triggered by: None
 Token: 31TxE8kyhU...
2025-06-21 23:12:50,773 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750527770771_9199.txt
2025-06-21 23:12:50,774 - signal_handler - INFO - [msg_1750527770771_9199] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:12:50,826 - signal_handler - INFO - [msg_1750527770771_9199] Successfully resolved info channel entity: Bot Info
2025-06-21 23:12:50,935 - signal_handler - INFO - [msg_1750527770771_9199] Message sent directly successfully using entity
2025-06-21 23:12:50,935 - signal_handler - INFO - [msg_1750527770771_9199] Message sent directly successfully
2025-06-21 23:12:50,936 - signal_handler - INFO - [msg_1750527770936_3133] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-21 23:12:50
[REPEAT] Trade: REAL

[MONEY] Star...
2025-06-21 23:12:50,937 - signal_handler - INFO - [msg_1750527770936_3133] Message queued with normal priority
2025-06-21 23:12:50,998 - signal_handler - INFO - [msg_1750527770936_3133] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:12:51,046 - signal_handler - INFO - [msg_1750527770936_3133] Successfully resolved info channel entity: Bot Info
2025-06-21 23:12:51,138 - signal_handler - INFO - [msg_1750527770936_3133] Message sent directly successfully using entity
2025-06-21 23:12:51,139 - signal_handler - INFO - [msg_1750527770936_3133] Message sent successfully via direct send method
2025-06-21 23:12:51,139 - signal_handler - INFO - [msg_1750527770936_3133] Message sent successfully on attempt 1
2025-06-21 23:13:34,062 - signal_handler - INFO - Received message from channel -1002093384030: [FIRE] [**Gorbagana Acceleration**](https://t.me/soul_sniper_bot?start=15_31TxE8kyhUJfq8JPeJNTdVPZei...
2025-06-21 23:13:34,063 - signal_handler - INFO - Setting channel_identifier to 'Solana Early Trending' based on chat_id -1002093384030
2025-06-21 23:13:34,063 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 23:13:34,063 - signal_handler - INFO - Found contract address with 'pump' suffix in Soul Sniper link: ********************************************
2025-06-21 23:13:34,063 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-21 23:13:34,064 - signal_handler - INFO - Detected signal from Solana Early Trending for token: ********************************************
2025-06-21 23:13:34,064 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=Solana Early Trending, Metrics Count=1
2025-06-21 23:13:34,064 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: ********************************************
2025-06-21 23:13:34,064 - signal_handler - INFO - Token ******************************************** cooldown expired. Processing signal.
2025-06-21 23:13:34,065 - signal_handler - INFO - Added signal to queue: ******************************************** from Solana Early Trending with confidence 0.5, GMGN channel: False
2025-06-21 23:13:34,065 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-21 23:13:34,068 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-21 23:13:34,069 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from Solana Early Trending (confidence: 0.50)
2025-06-21 23:13:34,069 - signal_handler - INFO - [msg_1750527814069_4354] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 23:13:34

 Token CA: 31TxE8kyhUJfq8JPeJNTdVPZeibQrcNnDbkvnigVp...
2025-06-21 23:13:34,069 - signal_handler - INFO - [msg_1750527814069_4354] Message queued with normal priority
2025-06-21 23:13:34,069 - signal_handler - INFO - Retrieved signal from queue: ******************************************** from Solana Early Trending
2025-06-21 23:13:34,133 - signal_handler - INFO - [msg_1750527814069_4354] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:13:34,183 - signal_handler - INFO - [msg_1750527814069_4354] Successfully resolved info channel entity: Bot Info
2025-06-21 23:13:34,262 - signal_handler - INFO - [msg_1750527814069_4354] Message sent directly successfully using entity
2025-06-21 23:13:34,262 - signal_handler - INFO - [msg_1750527814069_4354] Message sent successfully via direct send method
2025-06-21 23:13:34,262 - signal_handler - INFO - [msg_1750527814069_4354] Message sent successfully on attempt 1
2025-06-21 23:13:36,451 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$8686.0577(+210.2%)...
2025-06-21 23:13:36,451 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 23:13:36,451 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 23:13:36,451 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 23:13:36,452 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 23:13:36,452 - signal_handler - INFO - Found contract address with 'pump' suffix: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,452 - signal_handler - INFO - Found contract address: 3yf1RspqvwQiEbyLjRqh8UsjfK68HCMBq6aKEBBfh7uv
2025-06-21 23:13:36,453 - signal_handler - INFO - Using 'pump' address as highest priority: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,453 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,453 - signal_handler - INFO - Detected GMGN format
2025-06-21 23:13:36,453 - signal_handler - INFO - Found token symbol: 8686
2025-06-21 23:13:36,453 - signal_handler - INFO - Found FDV: 8686.0577 - 8.69K (+210.2%)
2025-06-21 23:13:36,454 - signal_handler - INFO - Detected GMGN channel signal for token: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,454 - signal_handler - INFO - Extracted signal: Token=GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 23:13:36,454 - signal_handler - INFO - Added signal to queue: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 23:13:36,454 - signal_handler - INFO - Calling direct signal callback for GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,456 - signal_handler - INFO - Direct signal processing completed for GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,457 - signal_handler - INFO - Signal forwarded to bot controller: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 23:13:36,457 - signal_handler - INFO - [msg_1750527816457_3958] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 23:13:36

 Token CA: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcup...
2025-06-21 23:13:36,457 - signal_handler - INFO - [msg_1750527816457_3958] Message queued with normal priority
2025-06-21 23:13:38,517 - signal_handler - INFO - [msg_1750527818517_8977] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-21 23:13:38

 Token: GVkSYFMuLsFqGpkmdhFMMRswLp...
2025-06-21 23:13:38,517 - signal_handler - INFO - [msg_1750527818517_8977] Message queued with normal priority
2025-06-21 23:13:38,517 - signal_handler - INFO - Retrieved signal from queue: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump from solana signal alert - gmgn
2025-06-21 23:13:38,519 - signal_handler - INFO - Message queue processor active with 2 messages pending
2025-06-21 23:13:38,519 - signal_handler - INFO - [msg_1750527816457_3958] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:13:38,599 - signal_handler - INFO - [msg_1750527816457_3958] Successfully resolved info channel entity: Bot Info
2025-06-21 23:13:38,676 - signal_handler - INFO - [msg_1750527816457_3958] Message sent directly successfully using entity
2025-06-21 23:13:38,677 - signal_handler - INFO - [msg_1750527816457_3958] Message sent successfully via direct send method
2025-06-21 23:13:38,677 - signal_handler - INFO - [msg_1750527816457_3958] Message sent successfully on attempt 1
2025-06-21 23:13:38,677 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 23:13:39,189 - signal_handler - INFO - [msg_1750527818517_8977] Resolving info channel entity for ID: -1002362136450
2025-06-21 23:13:39,240 - signal_handler - INFO - [msg_1750527818517_8977] Successfully resolved info channel entity: Bot Info
2025-06-21 23:13:39,321 - signal_handler - INFO - [msg_1750527818517_8977] Message sent directly successfully using entity
2025-06-21 23:13:39,321 - signal_handler - INFO - [msg_1750527818517_8977] Message sent successfully via direct send method
2025-06-21 23:13:39,321 - signal_handler - INFO - [msg_1750527818517_8977] Message sent successfully on attempt 1
