2025-06-27 16:05:12,919 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-27 16:05:12,919 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-27 16:05:12,920 - signal_handler - INFO - Loaded session string from C:\Users\<USER>\OneDrive\Documents\Mainnet 260625 one channel\session_string.json (length: 353)
2025-06-27 16:05:12,920 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_fresh_1751020512
2025-06-27 16:05:12,920 - signal_handler - INFO - Using session path: C:\Users\<USER>\OneDrive\Documents\Mainnet 260625 one channel\trading_real_session_fresh_1751020512
2025-06-27 16:05:12,922 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-27 16:05:12,929 - signal_handler - INFO - Direct signal callback registered
2025-06-27 16:05:12,930 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-27 16:05:12,931 - signal_handler - INFO - API ID loaded: True
2025-06-27 16:05:12,931 - signal_handler - INFO - API Hash loaded: True
2025-06-27 16:05:12,931 - signal_handler - INFO - Phone number loaded: True
2025-06-27 16:05:12,931 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-27 16:05:12,931 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-27 16:05:12,931 - signal_handler - INFO - Using saved session string
2025-06-27 16:05:12,932 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-27 16:05:13,430 - signal_handler - INFO - Checking authorization status...
2025-06-27 16:05:13,508 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-27 16:05:13,508 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-27 16:05:13,509 - signal_handler - INFO - Checking authorization status...
2025-06-27 16:05:13,594 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-27 16:05:13,595 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-27 16:05:13,595 - signal_handler - INFO - Checking authorization status...
2025-06-27 16:05:13,674 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-27 16:05:13,675 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-27 16:05:13,675 - signal_handler - INFO - Sending test message to verify connection to channel -1002501318635
2025-06-27 16:05:13,675 - signal_handler - INFO - Sending test message directly to channel ID: -1002501318635
2025-06-27 16:05:13,883 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-27 16:05:13,884 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-27 16:05:13,884 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-27 16:05:13,884 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-27 16:05:13,886 - signal_handler - INFO - Direct signal callback registered
2025-06-27 16:05:14,153 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-27 16:06:16,423 - signal_handler - DEBUG - Signal processing DISABLED - ignoring message: [REFRESH] Update: **$EMC**

[ROCKET] x2.4
[CHART] ...
