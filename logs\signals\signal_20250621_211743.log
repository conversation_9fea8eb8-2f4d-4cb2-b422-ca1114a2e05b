2025-06-21 21:17:43,583 - signal_handler - DEBUG - Loaded Telegram API ID: 27409872
2025-06-21 21:17:43,584 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-21 21:17:43,584 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\session_string.json (length: 353)
2025-06-21 21:17:43,584 - signal_handler - INFO - Using unique session name with timestamp: trading_real_session_1750520863
2025-06-21 21:17:43,584 - signal_handler - INFO - Using session path: c:\Users\<USER>\OneDrive\Documents\Mainnet 190625\trading_real_session_1750520863
2025-06-21 21:17:43,585 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-21 21:17:43,596 - signal_handler - INFO - Direct signal callback registered
2025-06-21 21:17:43,597 - signal_handler - INFO - Signal processing DISABLED - will only listen but not analyze signals
2025-06-21 21:17:43,597 - signal_handler - INFO - API ID loaded: True
2025-06-21 21:17:43,597 - signal_handler - INFO - API Hash loaded: True
2025-06-21 21:17:43,598 - signal_handler - INFO - Phone number loaded: True
2025-06-21 21:17:43,598 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 21:17:43,598 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-21 21:17:43,598 - signal_handler - INFO - Using saved session string
2025-06-21 21:17:43,599 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 21:17:44,737 - signal_handler - INFO - Checking authorization status...
2025-06-21 21:17:44,925 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 21:17:44,926 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 21:17:44,926 - signal_handler - INFO - Checking authorization status...
2025-06-21 21:17:45,126 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 21:17:45,126 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 21:17:45,127 - signal_handler - INFO - Checking authorization status...
2025-06-21 21:17:45,312 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 21:17:45,313 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 21:17:45,313 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 21:17:45,313 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 21:17:45,790 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 21:17:45,790 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-21 21:17:45,790 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-21 21:17:45,791 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-21 21:17:45,792 - signal_handler - INFO - Direct signal callback registered
2025-06-21 21:17:46,208 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-21 21:17:53,921 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 21:17:58,684 - signal_handler - INFO - [msg_1750520878680_4120] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-21 21:17:58

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-21 21:17:58,684 - signal_handler - INFO - Started Telegram message queue processor
2025-06-21 21:17:58,684 - signal_handler - INFO - [msg_1750520878680_4120] Message queued with normal priority
2025-06-21 21:17:58,704 - signal_handler - INFO - [msg_1750520878704_4999] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-21 21:17:58
[REPEAT] Trade: REAL

[MONEY] Sta...
2025-06-21 21:17:58,704 - signal_handler - INFO - [msg_1750520878704_4999] Message queued with normal priority
2025-06-21 21:17:58,878 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-21 21:17:58,878 - signal_handler - INFO - Message queue processor active with 2 messages pending
2025-06-21 21:17:58,878 - signal_handler - INFO - [msg_1750520878680_4120] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:17:59,104 - signal_handler - INFO - [msg_1750520878680_4120] Successfully resolved info channel entity: Bot Info
2025-06-21 21:17:59,359 - signal_handler - INFO - [msg_1750520878680_4120] Message sent directly successfully using entity
2025-06-21 21:17:59,359 - signal_handler - INFO - [msg_1750520878680_4120] Message sent successfully via direct send method
2025-06-21 21:17:59,360 - signal_handler - INFO - [msg_1750520878680_4120] Message sent successfully on attempt 1
2025-06-21 21:17:59,360 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 21:17:59,868 - signal_handler - INFO - [msg_1750520878704_4999] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:18:00,054 - signal_handler - INFO - [msg_1750520878704_4999] Successfully resolved info channel entity: Bot Info
2025-06-21 21:18:00,273 - signal_handler - INFO - [msg_1750520878704_4999] Message sent directly successfully using entity
2025-06-21 21:18:00,273 - signal_handler - INFO - [msg_1750520878704_4999] Message sent successfully via direct send method
2025-06-21 21:18:00,274 - signal_handler - INFO - [msg_1750520878704_4999] Message sent successfully on attempt 1
2025-06-21 21:18:27,046 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$21.2K(+482.7%)**
*...
2025-06-21 21:18:27,047 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 21:18:27,047 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 21:18:27,047 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 21:18:27,047 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:18:27,048 - signal_handler - INFO - Found contract address with 'pump' suffix: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump
2025-06-21 21:18:27,048 - signal_handler - INFO - Found contract address: kBjg4W7gsJjt9GjwbtwVXvuW4b9jHQYgmUgUEr6FF4T
2025-06-21 21:18:27,049 - signal_handler - INFO - Using 'pump' address as highest priority: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump
2025-06-21 21:18:27,049 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump
2025-06-21 21:18:27,050 - signal_handler - INFO - Detected GMGN format
2025-06-21 21:18:27,050 - signal_handler - INFO - Found token symbol: 21
2025-06-21 21:18:27,051 - signal_handler - INFO - Found FDV: 21.2K - 21.20 (+482.7%)
2025-06-21 21:18:27,051 - signal_handler - INFO - Detected GMGN channel signal for token: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump
2025-06-21 21:18:27,052 - signal_handler - INFO - Extracted signal: Token=Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 21:18:27,052 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-21 21:18:27,052 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-21 21:18:27,052 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-21 21:18:27,052 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-21 21:18:27,053 - signal_handler - INFO - Added signal to queue: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 21:18:27,053 - signal_handler - INFO - Calling direct signal callback for Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump
2025-06-21 21:18:29,264 - signal_handler - INFO - Direct signal processing completed for Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump
2025-06-21 21:18:29,265 - signal_handler - INFO - Signal forwarded to bot controller: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 21:18:29,265 - signal_handler - INFO - [msg_1750520909265_8844] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 21:18:27

 Token CA: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMp...
2025-06-21 21:18:29,265 - signal_handler - INFO - [msg_1750520909265_8844] Message queued with normal priority
2025-06-21 21:18:29,265 - signal_handler - INFO - [msg_1750520909265_2876] Queuing Telegram message: [GRAPH] [ANALYSIS COMPLETE] [?] 2025-06-21 21:18:29

 Token: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6Y...
2025-06-21 21:18:29,265 - signal_handler - INFO - [msg_1750520909265_2876] Message queued with normal priority
2025-06-21 21:18:29,265 - signal_handler - INFO - Retrieved signal from queue: Gyepjb7RWBJJ7KLns7S1VHNovQPBwG7fTG2fB6YMpump from solana signal alert - gmgn
2025-06-21 21:18:29,267 - signal_handler - INFO - [msg_1750520909265_8844] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:18:29,455 - signal_handler - INFO - [msg_1750520909265_8844] Successfully resolved info channel entity: Bot Info
2025-06-21 21:18:29,690 - signal_handler - INFO - [msg_1750520909265_8844] Message sent directly successfully using entity
2025-06-21 21:18:29,690 - signal_handler - INFO - [msg_1750520909265_8844] Message sent successfully via direct send method
2025-06-21 21:18:29,691 - signal_handler - INFO - [msg_1750520909265_8844] Message sent successfully on attempt 1
2025-06-21 21:18:29,691 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 21:18:30,193 - signal_handler - INFO - [msg_1750520909265_2876] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:18:30,384 - signal_handler - INFO - [msg_1750520909265_2876] Successfully resolved info channel entity: Bot Info
2025-06-21 21:18:30,611 - signal_handler - INFO - [msg_1750520909265_2876] Message sent directly successfully using entity
2025-06-21 21:18:30,611 - signal_handler - INFO - [msg_1750520909265_2876] Message sent successfully via direct send method
2025-06-21 21:18:30,611 - signal_handler - INFO - [msg_1750520909265_2876] Message sent successfully on attempt 1
2025-06-21 21:18:37,163 - signal_handler - INFO - Signal processing ENABLED - will analyze incoming signals
2025-06-21 21:18:37,163 - signal_handler - INFO - API ID loaded: True
2025-06-21 21:18:37,164 - signal_handler - INFO - API Hash loaded: True
2025-06-21 21:18:37,164 - signal_handler - INFO - Phone number loaded: True
2025-06-21 21:18:37,164 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-21 21:18:37,164 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-21 21:18:37,164 - signal_handler - INFO - Checking authorization status...
2025-06-21 21:18:37,520 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 21:18:37,520 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-21 21:18:37,520 - signal_handler - INFO - Checking authorization status...
2025-06-21 21:18:37,728 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 21:18:37,729 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-21 21:18:37,729 - signal_handler - INFO - Checking authorization status...
2025-06-21 21:18:37,936 - signal_handler - INFO - Successfully retrieved own user ID: 5423407329
2025-06-21 21:18:37,936 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-21 21:18:37,937 - signal_handler - INFO - Sending test message to verify connection to channel -1002362136450
2025-06-21 21:18:37,937 - signal_handler - INFO - Sending test message directly to channel ID: -1002362136450
2025-06-21 21:18:38,182 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-21 21:18:38,183 - signal_handler - INFO - [msg_1750520918183_2582] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-21 21:18:38
[REPEAT] Trade: REAL

[MONEY]...
2025-06-21 21:18:38,184 - signal_handler - INFO - [msg_1750520918183_2582] Message queued with normal priority
2025-06-21 21:18:38,212 - signal_handler - INFO - [msg_1750520918183_2582] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:18:38,399 - signal_handler - INFO - [msg_1750520918183_2582] Successfully resolved info channel entity: Bot Info
2025-06-21 21:18:38,684 - signal_handler - INFO - [msg_1750520918183_2582] Message sent directly successfully using entity
2025-06-21 21:18:38,684 - signal_handler - INFO - [msg_1750520918183_2582] Message sent successfully via direct send method
2025-06-21 21:18:38,684 - signal_handler - INFO - [msg_1750520918183_2582] Message sent successfully on attempt 1
2025-06-21 21:18:38,685 - signal_handler - WARNING - Signal listener is already started. Skipping.
2025-06-21 21:18:38,685 - signal_handler - INFO - Direct signal callback registered
2025-06-21 21:18:38,686 - signal_handler - INFO - [msg_1750520918686_9388] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: REAL
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capital: 0.0...
2025-06-21 21:18:38,686 - signal_handler - INFO - [msg_1750520918686_9388] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:18:38,939 - signal_handler - INFO - [msg_1750520918686_9388] Successfully resolved info channel entity: Bot Info
2025-06-21 21:18:39,160 - signal_handler - INFO - [msg_1750520918686_9388] Message sent directly successfully using entity
2025-06-21 21:18:39,160 - signal_handler - INFO - [msg_1750520918686_9388] Message sent directly successfully
2025-06-21 21:20:42,377 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$18K(+329.7%)**
**[...
2025-06-21 21:20:42,377 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 21:20:42,377 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 21:20:42,378 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 21:20:42,378 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:20:42,378 - signal_handler - INFO - Found contract address with 'pump' suffix: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:20:42,378 - signal_handler - INFO - Found contract address: MNhBbrscBPmeid54buiqSgyWa4D8PY6uKHoK2wJsTJN
2025-06-21 21:20:42,379 - signal_handler - INFO - Using 'pump' address as highest priority: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:20:42,379 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:20:42,379 - signal_handler - INFO - Detected GMGN format
2025-06-21 21:20:42,379 - signal_handler - INFO - Found token symbol: 18K
2025-06-21 21:20:42,379 - signal_handler - INFO - Found token full name: +329.7%
2025-06-21 21:20:42,380 - signal_handler - INFO - Found FDV: 18K - 18.00K (+329.7%)
2025-06-21 21:20:42,380 - signal_handler - INFO - Detected GMGN channel signal for token: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:20:42,380 - signal_handler - INFO - Extracted signal: Token=C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump, Source=solana signal alert - gmgn, Metrics Count=6
2025-06-21 21:20:42,380 - signal_handler - INFO - Added signal to queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 21:20:42,380 - signal_handler - INFO - Calling direct signal callback for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:20:42,383 - signal_handler - INFO - Direct signal processing completed for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:20:42,384 - signal_handler - INFO - Signal forwarded to bot controller: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 21:20:42,384 - signal_handler - INFO - Retrieved signal from queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from solana signal alert - gmgn
2025-06-21 21:20:42,386 - signal_handler - INFO - [msg_1750521042385_2414] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 21:20:42

 Token CA: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLAp...
2025-06-21 21:20:42,386 - signal_handler - INFO - [msg_1750521042385_2414] Message queued with normal priority
2025-06-21 21:20:47,247 - signal_handler - INFO - [msg_1750521042385_2414] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:20:47,437 - signal_handler - INFO - [msg_1750521042385_2414] Successfully resolved info channel entity: Bot Info
2025-06-21 21:20:47,666 - signal_handler - INFO - [msg_1750521042385_2414] Message sent directly successfully using entity
2025-06-21 21:20:47,667 - signal_handler - INFO - [msg_1750521042385_2414] Message sent successfully via direct send method
2025-06-21 21:20:47,667 - signal_handler - INFO - [msg_1750521042385_2414] Message sent successfully on attempt 1
2025-06-21 21:20:50,026 - signal_handler - INFO - [msg_1750521050026_1323] [SELL NOTIFICATION]: [GREEN] [BUY EXECUTED] [?] 2025-06-21 21:20:50

 Signal Source: solana signal alert - gmgn
 Token: C...
2025-06-21 21:20:50,027 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750521050026_1323.txt
2025-06-21 21:20:50,028 - signal_handler - INFO - [msg_1750521050026_1323] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:20:50,215 - signal_handler - INFO - [msg_1750521050026_1323] Successfully resolved info channel entity: Bot Info
2025-06-21 21:20:50,448 - signal_handler - INFO - [msg_1750521050026_1323] Message sent directly successfully using entity
2025-06-21 21:20:50,448 - signal_handler - INFO - [msg_1750521050026_1323] Message sent directly successfully
2025-06-21 21:20:50,449 - signal_handler - INFO - [msg_1750521050449_7992] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (REAL) - C9OQ8WSI

 Timestamp: 2025-06-21 21:20:50
[REPEAT] Trade: R...
2025-06-21 21:20:50,449 - signal_handler - INFO - [msg_1750521050449_7992] Message queued with normal priority
2025-06-21 21:20:50,467 - signal_handler - INFO - [msg_1750521050449_7992] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:20:50,658 - signal_handler - INFO - [msg_1750521050449_7992] Successfully resolved info channel entity: Bot Info
2025-06-21 21:20:50,880 - signal_handler - INFO - [msg_1750521050449_7992] Message sent directly successfully using entity
2025-06-21 21:20:50,880 - signal_handler - INFO - [msg_1750521050449_7992] Message sent successfully via direct send method
2025-06-21 21:20:50,881 - signal_handler - INFO - [msg_1750521050449_7992] Message sent successfully on attempt 1
2025-06-21 21:21:14,810 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$8597.8153(+214.4%)...
2025-06-21 21:21:14,810 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 21:21:14,810 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 21:21:14,810 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 21:21:14,811 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:21:14,811 - signal_handler - INFO - Found contract address with 'pump' suffix: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump
2025-06-21 21:21:14,811 - signal_handler - INFO - Found contract address: 6WAZxPHkH6ZF3RJnntTyDq617UDUKFJzfDLMWozSeaQF
2025-06-21 21:21:14,811 - signal_handler - INFO - Using 'pump' address as highest priority: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump
2025-06-21 21:21:14,811 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump
2025-06-21 21:21:14,812 - signal_handler - INFO - Detected GMGN format
2025-06-21 21:21:14,812 - signal_handler - INFO - Found token symbol: 8597
2025-06-21 21:21:14,812 - signal_handler - INFO - Found FDV: 8597.8153 - 8.60K (+214.4%)
2025-06-21 21:21:14,812 - signal_handler - INFO - Detected GMGN channel signal for token: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump
2025-06-21 21:21:14,812 - signal_handler - INFO - Extracted signal: Token=6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 21:21:14,812 - signal_handler - INFO - Added signal to queue: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 21:21:14,812 - signal_handler - INFO - Calling direct signal callback for 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump
2025-06-21 21:21:14,815 - signal_handler - INFO - Direct signal processing completed for 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump
2025-06-21 21:21:14,816 - signal_handler - INFO - Signal forwarded to bot controller: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 21:21:14,816 - signal_handler - INFO - [msg_1750521074816_3804] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 21:21:14

 Token CA: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenp...
2025-06-21 21:21:14,816 - signal_handler - INFO - [msg_1750521074816_3804] Message queued with normal priority
2025-06-21 21:21:16,824 - signal_handler - INFO - [msg_1750521076824_4769] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-21 21:21:16

 Token: 6LdgxcEJAV9d6ca4upQZznMHS6...
2025-06-21 21:21:16,825 - signal_handler - INFO - [msg_1750521076824_4769] Message queued with normal priority
2025-06-21 21:21:16,825 - signal_handler - INFO - Retrieved signal from queue: 6LdgxcEJAV9d6ca4upQZznMHS6uatwJEhgKeGrenpump from solana signal alert - gmgn
2025-06-21 21:21:16,828 - signal_handler - INFO - [msg_1750521074816_3804] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:21:20,157 - signal_handler - INFO - Received sell notification: [PURPLE] [SELL [?] TP1 HIT (197.1% >= 11.6%)] [?] 2025-06-21 21:21:20
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trigger...
2025-06-21 21:21:20,158 - signal_handler - INFO - [msg_1750521080157_4829] [SELL NOTIFICATION]:  [SELL [?] TP1 HIT (197.1% >= 11.6%)] [?] 2025-06-21 21:21:20

 Triggered by: solana signal alert - ...
2025-06-21 21:21:20,159 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1750521080157_4829.txt
2025-06-21 21:21:20,160 - signal_handler - INFO - [msg_1750521080157_4829] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:21:20,180 - signal_handler - INFO - [msg_1750521074816_3804] Successfully resolved info channel entity: Bot Info
2025-06-21 21:21:20,363 - signal_handler - INFO - [msg_1750521080157_4829] Successfully resolved info channel entity: Bot Info
2025-06-21 21:21:20,425 - signal_handler - INFO - [msg_1750521074816_3804] Message sent directly successfully using entity
2025-06-21 21:21:20,425 - signal_handler - INFO - [msg_1750521074816_3804] Message sent successfully via direct send method
2025-06-21 21:21:20,426 - signal_handler - INFO - [msg_1750521074816_3804] Message sent successfully on attempt 1
2025-06-21 21:21:20,426 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 21:21:20,596 - signal_handler - INFO - [msg_1750521080157_4829] Message sent directly successfully using entity
2025-06-21 21:21:20,597 - signal_handler - INFO - [msg_1750521080157_4829] Message sent directly successfully
2025-06-21 21:21:20,598 - signal_handler - INFO - [msg_1750521080597_9409] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-21 21:21:20
[REPEAT] Trade: REAL

[MONEY] Star...
2025-06-21 21:21:20,598 - signal_handler - INFO - [msg_1750521080597_9409] Message queued with normal priority
2025-06-21 21:21:20,929 - signal_handler - INFO - [msg_1750521076824_4769] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:21:21,128 - signal_handler - INFO - [msg_1750521076824_4769] Successfully resolved info channel entity: Bot Info
2025-06-21 21:21:21,349 - signal_handler - INFO - [msg_1750521076824_4769] Message sent directly successfully using entity
2025-06-21 21:21:21,349 - signal_handler - INFO - [msg_1750521076824_4769] Message sent successfully via direct send method
2025-06-21 21:21:21,349 - signal_handler - INFO - [msg_1750521076824_4769] Message sent successfully on attempt 1
2025-06-21 21:21:21,349 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 21:21:21,859 - signal_handler - INFO - [msg_1750521080597_9409] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:21:22,175 - signal_handler - INFO - [msg_1750521080597_9409] Successfully resolved info channel entity: Bot Info
2025-06-21 21:21:22,409 - signal_handler - INFO - [msg_1750521080597_9409] Message sent directly successfully using entity
2025-06-21 21:21:22,410 - signal_handler - INFO - [msg_1750521080597_9409] Message sent successfully via direct send method
2025-06-21 21:21:22,410 - signal_handler - INFO - [msg_1750521080597_9409] Message sent successfully on attempt 1
2025-06-21 21:22:20,448 - signal_handler - INFO - Received message from channel -1002017857449: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump...
2025-06-21 21:22:20,448 - signal_handler - INFO - Setting channel_identifier to 'PEPE CALLS' based on chat_id -1002017857449
2025-06-21 21:22:20,449 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:22:20,449 - signal_handler - INFO - Found contract address with 'pump' suffix: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,450 - signal_handler - INFO - Using 'pump' address as highest priority: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,450 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,450 - signal_handler - INFO - Detected signal from PEPE CALLS for token: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,451 - signal_handler - INFO - Extracted signal: Token=C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump, Source=PEPE CALLS, Metrics Count=1
2025-06-21 21:22:20,451 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,451 - signal_handler - INFO - Token C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump cooldown expired. Processing signal.
2025-06-21 21:22:20,452 - signal_handler - INFO - Added signal to queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from PEPE CALLS with confidence 0.5, GMGN channel: False
2025-06-21 21:22:20,452 - signal_handler - INFO - Calling direct signal callback for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,454 - signal_handler - INFO - Direct signal processing completed for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:20,455 - signal_handler - INFO - Signal forwarded to bot controller: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from PEPE CALLS (confidence: 0.50)
2025-06-21 21:22:20,455 - signal_handler - INFO - Retrieved signal from queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from PEPE CALLS
2025-06-21 21:22:20,457 - signal_handler - INFO - [msg_1750521140457_6367] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 21:22:20

 Token CA: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLAp...
2025-06-21 21:22:20,457 - signal_handler - INFO - [msg_1750521140457_6367] Message queued with normal priority
2025-06-21 21:22:20,483 - signal_handler - INFO - [msg_1750521140457_6367] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:22:20,706 - signal_handler - INFO - [msg_1750521140457_6367] Successfully resolved info channel entity: Bot Info
2025-06-21 21:22:21,021 - signal_handler - INFO - [msg_1750521140457_6367] Message sent directly successfully using entity
2025-06-21 21:22:21,022 - signal_handler - INFO - [msg_1750521140457_6367] Message sent successfully via direct send method
2025-06-21 21:22:21,022 - signal_handler - INFO - [msg_1750521140457_6367] Message sent successfully on attempt 1
2025-06-21 21:22:40,620 - signal_handler - INFO - Received message from channel -1001509251052: [ALARM] NEW POTENTIAL GEM!

**space dog** [?] **$franz**
`C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApu...
2025-06-21 21:22:40,620 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:22:40,621 - signal_handler - INFO - Found contract address with 'pump' suffix: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:40,621 - signal_handler - INFO - Found contract address: GUx9emhw4NfhH44jKjJUDrQCYmx4afNwfyAQntrYH1nc
2025-06-21 21:22:40,621 - signal_handler - INFO - Found contract address: LeckTHCG66D9CcZoecas8P8AGkNmqbXfAt6LXCKDWhq
2025-06-21 21:22:40,621 - signal_handler - INFO - Found contract address: suqh5sHtr8HyJ7q8scBimULPkPpA557prMG47xCHQfK
2025-06-21 21:22:40,622 - signal_handler - INFO - Found contract address: 7m3z9QQZutBAGCVTzN1ouc5T26QAQ3yYQnBB3rPRyzpn
2025-06-21 21:22:40,622 - signal_handler - INFO - Found contract address: 4BdKaxN8G6ka4GYtQQWk4G4dZRUTX2vQH9GcXdBREFUk
2025-06-21 21:22:40,622 - signal_handler - INFO - Using 'pump' address as highest priority: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:40,622 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:40,622 - signal_handler - INFO - Detected Pepe Calls format
2025-06-21 21:22:40,623 - signal_handler - INFO - Found token symbol: franz
2025-06-21 21:22:40,623 - signal_handler - INFO - Extracted signal: Token=C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump, Source=None, Metrics Count=2
2025-06-21 21:22:40,623 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:40,624 - signal_handler - INFO - Token C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump cooldown expired. Processing signal.
2025-06-21 21:22:40,624 - signal_handler - INFO - Added signal to queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from None with confidence 0.5, GMGN channel: False
2025-06-21 21:22:40,624 - signal_handler - INFO - Calling direct signal callback for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:40,627 - signal_handler - INFO - Direct signal processing completed for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:22:40,627 - signal_handler - INFO - Signal forwarded to bot controller: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from None (confidence: 0.50)
2025-06-21 21:22:40,627 - signal_handler - DEBUG - QUEUE DEDUP: Skipping C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump (processed 20.2s ago)
2025-06-21 21:24:03,832 - signal_handler - INFO - Received message from channel -1002093384030: [FIRE] [**space dog**](https://t.me/soul_sniper_bot?start=15_C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRL...
2025-06-21 21:24:03,832 - signal_handler - INFO - Setting channel_identifier to 'Solana Early Trending' based on chat_id -1002093384030
2025-06-21 21:24:03,833 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:24:03,833 - signal_handler - INFO - Found contract address with 'pump' suffix in Soul Sniper link: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:24:03,833 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:24:03,834 - signal_handler - INFO - Detected signal from Solana Early Trending for token: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:24:03,834 - signal_handler - INFO - Extracted signal: Token=C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump, Source=Solana Early Trending, Metrics Count=1
2025-06-21 21:24:03,834 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:24:03,835 - signal_handler - INFO - Token C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump cooldown expired. Processing signal.
2025-06-21 21:24:03,835 - signal_handler - INFO - Added signal to queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from Solana Early Trending with confidence 0.5, GMGN channel: False
2025-06-21 21:24:03,835 - signal_handler - INFO - Calling direct signal callback for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:24:03,839 - signal_handler - INFO - Direct signal processing completed for C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump
2025-06-21 21:24:03,839 - signal_handler - INFO - Signal forwarded to bot controller: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from Solana Early Trending (confidence: 0.50)
2025-06-21 21:24:03,839 - signal_handler - INFO - [msg_1750521243839_1167] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 21:24:03

 Token CA: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLAp...
2025-06-21 21:24:03,839 - signal_handler - INFO - [msg_1750521243839_1167] Message queued with normal priority
2025-06-21 21:24:03,840 - signal_handler - INFO - Retrieved signal from queue: C9oq8wSiPwZapWntDG91YdMfr1UiRaMULQjyGRLApump from Solana Early Trending
2025-06-21 21:24:03,853 - signal_handler - INFO - [msg_1750521243839_1167] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:24:04,002 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$19.4K(+489.1%)**
*...
2025-06-21 21:24:04,003 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-21 21:24:04,003 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-21 21:24:04,003 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-21 21:24:04,004 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-21 21:24:04,004 - signal_handler - INFO - Found contract address: ********************************************
2025-06-21 21:24:04,004 - signal_handler - INFO - Found contract address: 4dkoMpQpaA2mcTTQbjMijG8NtmN8GCtEvbETQnkpxkDS
2025-06-21 21:24:04,005 - signal_handler - INFO - Found 2 unique token addresses
2025-06-21 21:24:04,005 - signal_handler - INFO - Token address: ********************************************
2025-06-21 21:24:04,005 - signal_handler - INFO - Token address: 4dkoMpQpaA2mcTTQbjMijG8NtmN8GCtEvbETQnkpxkDS
2025-06-21 21:24:04,005 - signal_handler - INFO - Detected GMGN format
2025-06-21 21:24:04,005 - signal_handler - INFO - Found token symbol: 19
2025-06-21 21:24:04,005 - signal_handler - INFO - Found FDV: 19.4K - 19.40 (+489.1%)
2025-06-21 21:24:04,005 - signal_handler - INFO - Detected GMGN channel signal for token: ********************************************
2025-06-21 21:24:04,005 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-21 21:24:04,006 - signal_handler - INFO - Added signal to queue: ******************************************** from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-21 21:24:04,006 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-21 21:24:04,008 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-21 21:24:04,008 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from solana signal alert - gmgn (confidence: 0.50)
2025-06-21 21:24:04,008 - signal_handler - INFO - [msg_1750521244008_7966] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-21 21:24:04

 Token CA: 3q7zmVgyQZaARS88J1B1wQYQgwMfweHoMfLS9AbyN...
2025-06-21 21:24:04,009 - signal_handler - INFO - [msg_1750521244008_7966] Message queued with normal priority
2025-06-21 21:24:05,861 - signal_handler - INFO - [msg_1750521245861_7450] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-21 21:24:05

 Token: 3q7zmVgyQZaARS88J1B1wQYQgw...
2025-06-21 21:24:05,861 - signal_handler - INFO - [msg_1750521245861_7450] Message queued with normal priority
2025-06-21 21:24:05,862 - signal_handler - INFO - Retrieved signal from queue: ******************************************** from solana signal alert - gmgn
2025-06-21 21:24:05,864 - signal_handler - INFO - [msg_1750521243839_1167] Successfully resolved info channel entity: Bot Info
2025-06-21 21:24:06,102 - signal_handler - INFO - [msg_1750521243839_1167] Message sent directly successfully using entity
2025-06-21 21:24:06,102 - signal_handler - INFO - [msg_1750521243839_1167] Message sent successfully via direct send method
2025-06-21 21:24:06,102 - signal_handler - INFO - [msg_1750521243839_1167] Message sent successfully on attempt 1
2025-06-21 21:24:06,103 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 21:24:06,607 - signal_handler - INFO - [msg_1750521244008_7966] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:24:06,857 - signal_handler - INFO - [msg_1750521244008_7966] Successfully resolved info channel entity: Bot Info
2025-06-21 21:24:07,086 - signal_handler - INFO - [msg_1750521244008_7966] Message sent directly successfully using entity
2025-06-21 21:24:07,087 - signal_handler - INFO - [msg_1750521244008_7966] Message sent successfully via direct send method
2025-06-21 21:24:07,087 - signal_handler - INFO - [msg_1750521244008_7966] Message sent successfully on attempt 1
2025-06-21 21:24:07,087 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-21 21:24:07,087 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-21 21:24:07,590 - signal_handler - INFO - [msg_1750521245861_7450] Resolving info channel entity for ID: -1002362136450
2025-06-21 21:24:07,849 - signal_handler - INFO - [msg_1750521245861_7450] Successfully resolved info channel entity: Bot Info
2025-06-21 21:24:08,135 - signal_handler - INFO - [msg_1750521245861_7450] Message sent directly successfully using entity
2025-06-21 21:24:08,135 - signal_handler - INFO - [msg_1750521245861_7450] Message sent successfully via direct send method
2025-06-21 21:24:08,136 - signal_handler - INFO - [msg_1750521245861_7450] Message sent successfully on attempt 1
